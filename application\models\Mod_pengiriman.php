<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Pengiriman
 * Mengatur data pengiriman dan detailnya
 * Terintegrasi dengan pesanan dan stok gudang
 */
class Mod_pengiriman extends CI_Model
{
    var $table = 'pengiriman';
    var $table_detail = 'pengiriman_detail';
    var $column_search = array(
        'p.nomor_pengiriman', 
        'p.tanggal_pengiriman', 
        'pel.nama as nama_pelanggan', 
        'pes.nomor_pesanan',
        'p.ekspedisi',
        'p.nomor_resi',
        'p.status', 
        'p.keterangan'
    );
    var $column_order = array(
        'p.id', 
        'p.nomor_pengiriman', 
        'p.tanggal_pengiriman', 
        'pel.nama', 
        'pes.nomor_pesanan',
        'p.ekspedisi',
        'p.nomor_resi',
        'p.status',
        'p.total_item',
        'p.total_qty'
    );
    var $order = array('p.id' => 'desc');

    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    private function _get_datatables_query()
    {
        $this->db->select('
            p.id,
            p.nomor_pengiriman,
            p.tanggal_pengiriman,
            p.id_pesanan,
            p.id_pelanggan,
            p.alamat_pengiriman,
            p.ekspedisi,
            p.nomor_resi,
            p.biaya_pengiriman,
            p.status,
            p.total_item,
            p.total_qty,
            p.total_berat,
            p.estimasi_tiba,
            p.tanggal_dikirim,
            p.tanggal_diterima,
            p.penerima,
            p.keterangan,
            p.created_by,
            p.updated_by,
            p.created_at,
            p.updated_at,
            pel.nama as nama_pelanggan,
            pel.kode as kode_pelanggan,
            pel.alamat as alamat_pelanggan,
            pel.no_telepon,
            pes.nomor_pesanan,
            pes.tanggal_pesanan,
            pes.total_harga as total_pesanan
        ');
        $this->db->from($this->table . ' p');
        $this->db->join('pelanggan pel', 'p.id_pelanggan = pel.id', 'left');
        $this->db->join('pesanan pes', 'p.id_pesanan = pes.id', 'left');

        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_POST['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_POST['search']['value']);
                } else {
                    $this->db->or_like($item, $_POST['search']['value']);
                }

                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_POST['order'])) {
            $this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables()
    {
        $this->_get_datatables_query();
        if ($_POST['length'] != -1)
            $this->db->limit($_POST['length'], $_POST['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered()
    {
        $this->_get_datatables_query();
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all()
    {
        $this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // ===== HEADER PENGIRIMAN METHODS =====

    // Insert header pengiriman
    function insert($data)
    {
        $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    // Update header pengiriman
    function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table, $data);
    }

    // Get header pengiriman
    function get($id)
    {
        $this->db->select('
            p.*,
            pel.kode as kode_pelanggan,
            pel.nama as nama_pelanggan,
            pel.alamat as alamat_pelanggan,
            pel.no_telepon,
            pes.nomor_pesanan,
            pes.tanggal_pesanan,
            pes.total_harga as total_pesanan
        ');
        $this->db->from('pengiriman p');
        $this->db->join('pelanggan pel', 'p.id_pelanggan = pel.id', 'left');
        $this->db->join('pesanan pes', 'p.id_pesanan = pes.id', 'left');
        $this->db->where('p.id', $id);
        return $this->db->get()->row();
    }

    // Delete header pengiriman (cascade delete detail)
    function delete($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table);
    }

    // Generate nomor pengiriman otomatis
    function generate_nomor_pengiriman()
    {
        $today = date('Ymd');
        $prefix = 'PGR-' . $today . '-';
        
        $this->db->select('nomor_pengiriman');
        $this->db->from($this->table);
        $this->db->like('nomor_pengiriman', $prefix, 'after');
        $this->db->order_by('nomor_pengiriman', 'DESC');
        $this->db->limit(1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            $last_nomor = $query->row()->nomor_pengiriman;
            $last_number = (int) substr($last_nomor, -4);
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }
        
        return $prefix . str_pad($new_number, 4, '0', STR_PAD_LEFT);
    }

    // Get pesanan yang bisa dikirim (status diproses)
    function get_pesanan_for_shipping()
    {
        try {
            $this->db->select('
                p.id,
                p.nomor_pesanan,
                p.tanggal_pesanan,
                p.total_item,
                p.total_qty,
                p.total_harga,
                pel.nama as nama_pelanggan,
                pel.alamat as alamat_pelanggan,
                pel.no_telepon
            ');
            $this->db->from('pesanan p');
            $this->db->join('pelanggan pel', 'p.id_pelanggan = pel.id', 'left');

            // Try with 'diproses' first, if no results try other statuses
            $this->db->where('p.status', 'diproses');
            $this->db->order_by('p.tanggal_pesanan', 'DESC');
            $result = $this->db->get()->result();

            // If no 'diproses' orders, try 'pending' or other statuses
            if (empty($result)) {
                $this->db->select('
                    p.id,
                    p.nomor_pesanan,
                    p.tanggal_pesanan,
                    p.total_item,
                    p.total_qty,
                    p.total_harga,
                    pel.nama as nama_pelanggan,
                    pel.alamat as alamat_pelanggan,
                    pel.no_telepon
                ');
                $this->db->from('pesanan p');
                $this->db->join('pelanggan pel', 'p.id_pelanggan = pel.id', 'left');
                $this->db->where_in('p.status', array('pending', 'confirmed', 'processing'));
                $this->db->order_by('p.tanggal_pesanan', 'DESC');
                $this->db->limit(10); // Limit for safety
                $result = $this->db->get()->result();
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'Error in get_pesanan_for_shipping: ' . $e->getMessage());
            return array();
        }
    }

    // Get detail pesanan untuk pengiriman
    function get_pesanan_detail($id_pesanan)
    {
        $this->db->select('
            pd.id,
            pd.id_barang,
            pd.qty,
            pd.harga_satuan,
            pd.subtotal,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            COALESCE(shipped.qty_shipped, 0) as qty_shipped,
            (pd.qty - COALESCE(shipped.qty_shipped, 0)) as qty_remaining
        ');
        $this->db->from('pesanan_detail pd');
        $this->db->join('barang b', 'pd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        
        // Subquery untuk menghitung qty yang sudah dikirim
        $this->db->join('(
            SELECT 
                pd2.id_barang,
                SUM(pgd.qty_dikirim) as qty_shipped
            FROM pengiriman_detail pgd
            JOIN pengiriman pg ON pgd.id_pengiriman = pg.id
            JOIN pesanan_detail pd2 ON pgd.id_barang = pd2.id_barang
            WHERE pg.id_pesanan = ' . (int)$id_pesanan . '
            AND pg.status NOT IN ("cancelled")
            GROUP BY pd2.id_barang
        ) shipped', 'pd.id_barang = shipped.id_barang', 'left');
        
        $this->db->where('pd.id_pesanan', $id_pesanan);
        $this->db->order_by('b.nama_barang', 'ASC');
        return $this->db->get()->result();
    }

    // Get gudang yang memiliki stok untuk barang tertentu
    function get_gudang_with_stock($id_barang)
    {
        $this->db->select('
            g.id,
            g.kode_gudang,
            g.nama_gudang,
            COALESCE(sb.qty_terakhir, 0) as stok_tersedia
        ');
        $this->db->from('gudang g');
        $this->db->join('stok_barang sb', 'g.id = sb.id_gudang AND sb.id_barang = ' . (int)$id_barang, 'left');
        $this->db->where('g.aktif', 1);
        $this->db->where('COALESCE(sb.qty_terakhir, 0) >', 0);
        $this->db->order_by('g.nama_gudang', 'ASC');
        return $this->db->get()->result();
    }

    // ===== DETAIL PENGIRIMAN METHODS =====

    // Get detail pengiriman
    function get_detail($id_pengiriman)
    {
        $this->db->select('
            pd.*,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            s.nama_satuan,
            g.kode_gudang,
            g.nama_gudang
        ');
        $this->db->from($this->table_detail . ' pd');
        $this->db->join('barang b', 'pd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->join('gudang g', 'pd.id_gudang = g.id', 'left');
        $this->db->where('pd.id_pengiriman', $id_pengiriman);
        $this->db->order_by('b.nama_barang', 'ASC');
        return $this->db->get()->result();
    }

    // Insert detail pengiriman
    function insert_detail($data)
    {
        $this->db->insert($this->table_detail, $data);
        return $this->db->insert_id();
    }

    // Update detail pengiriman
    function update_detail($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->update($this->table_detail, $data);
    }

    // Get single detail pengiriman
    function get_detail_by_id($id)
    {
        $this->db->select('
            pd.*,
            b.kode_barang,
            b.nama_barang,
            b.satuan_id,
            s.nama_satuan
        ');
        $this->db->from($this->table_detail . ' pd');
        $this->db->join('barang b', 'pd.id_barang = b.id', 'left');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->where('pd.id', $id);
        return $this->db->get()->row();
    }

    // Delete detail pengiriman
    function delete_detail($id)
    {
        $this->db->where('id', $id);
        $this->db->delete($this->table_detail);
    }

    // Update total pengiriman berdasarkan detail
    function update_total_pengiriman($id_pengiriman)
    {
        $this->db->select('
            COUNT(*) as total_item,
            SUM(qty_dikirim) as total_qty,
            SUM(total_berat) as total_berat
        ');
        $this->db->from($this->table_detail);
        $this->db->where('id_pengiriman', $id_pengiriman);
        $result = $this->db->get()->row();

        $update_data = array(
            'total_item' => $result->total_item,
            'total_qty' => $result->total_qty ?: 0,
            'total_berat' => $result->total_berat ?: 0
        );

        $this->db->where('id', $id_pengiriman);
        $this->db->update($this->table, $update_data);
    }

    // Validasi stok sebelum pengiriman
    function validate_stock($id_barang, $id_gudang, $qty_required, $exclude_detail_id = null)
    {
        // Get current stock
        $this->db->select('qty_terakhir');
        $this->db->from('stok_barang');
        $this->db->where('id_barang', $id_barang);
        $this->db->where('id_gudang', $id_gudang);
        $stock_query = $this->db->get();

        $current_stock = $stock_query->num_rows() > 0 ? $stock_query->row()->qty_terakhir : 0;

        // Get reserved stock (from other draft shipments)
        $this->db->select('SUM(qty_dikirim) as reserved_qty');
        $this->db->from($this->table_detail . ' pd');
        $this->db->join($this->table . ' p', 'pd.id_pengiriman = p.id');
        $this->db->where('pd.id_barang', $id_barang);
        $this->db->where('pd.id_gudang', $id_gudang);
        $this->db->where('p.status', 'draft');

        if ($exclude_detail_id) {
            $this->db->where('pd.id !=', $exclude_detail_id);
        }

        $reserved_query = $this->db->get();
        $reserved_stock = $reserved_query->row()->reserved_qty ?: 0;

        $available_stock = $current_stock - $reserved_stock;

        return array(
            'current_stock' => $current_stock,
            'reserved_stock' => $reserved_stock,
            'available_stock' => $available_stock,
            'is_sufficient' => $available_stock >= $qty_required
        );
    }

    // Get dropdown data
    function get_pelanggan_dropdown()
    {
        try {
            $this->db->select('id, kode, nama, alamat, no_telepon');
            $this->db->from('pelanggan');

            // Try with aktif = 1 first
            $this->db->where('aktif', 1);
            $this->db->order_by('nama', 'ASC');
            $result = $this->db->get()->result();

            // If no active customers, get all customers
            if (empty($result)) {
                $this->db->select('id, kode, nama, alamat, no_telepon');
                $this->db->from('pelanggan');
                $this->db->order_by('nama', 'ASC');
                $this->db->limit(50); // Limit for safety
                $result = $this->db->get()->result();
            }

            return $result;
        } catch (Exception $e) {
            log_message('error', 'Error in get_pelanggan_dropdown: ' . $e->getMessage());
            return array();
        }
    }

    function get_barang_dropdown()
    {
        $this->db->select('
            b.id,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            b.satuan_id,
            s.nama_satuan
        ');
        $this->db->from('barang b');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        $this->db->where('b.aktif', 1);
        $this->db->order_by('b.nama_barang', 'ASC');
        return $this->db->get()->result();
    }

    function get_gudang_dropdown()
    {
        $this->db->select('id, kode_gudang, nama_gudang');
        $this->db->from('gudang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_gudang', 'ASC');
        return $this->db->get()->result();
    }

    // Finalisasi pengiriman dan update stok
    function finalize_pengiriman($id, $user_final)
    {
        $this->db->trans_start();

        // Update status pengiriman
        $data = array(
            'status' => 'shipped',
            'tanggal_dikirim' => date('Y-m-d H:i:s'),
            'updated_by' => $user_final
        );

        $this->db->where('id', $id);
        $this->db->where('status', 'prepared');
        $this->db->update($this->table, $data);

        if ($this->db->affected_rows() == 0) {
            $this->db->trans_rollback();
            return false;
        }

        // Update stok untuk setiap detail
        $details = $this->get_detail($id);
        foreach ($details as $detail) {
            // Insert stok movement
            $movement_data = array(
                'tanggal' => date('Y-m-d H:i:s'),
                'id_barang' => $detail->id_barang,
                'id_gudang' => $detail->id_gudang,
                'tipe_transaksi' => 'penjualan',
                'qty_out' => $detail->qty_dikirim,
                'keterangan' => 'Pengiriman: ' . $this->get($id)->nomor_pengiriman,
                'ref_transaksi' => $this->get($id)->nomor_pengiriman,
                'user_input' => $user_final
            );

            $this->db->insert('stok_movement', $movement_data);
        }

        $this->db->trans_complete();
        return $this->db->trans_status();
    }
}
