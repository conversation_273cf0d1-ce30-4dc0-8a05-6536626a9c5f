<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h3 class="card-title"><i class="fa fa-shipping-fast text-blue"></i> Data Pengiriman</h3>
                        <div class="text-right">
                            <button type="button" class="btn btn-sm btn-outline-primary add" onclick="add()" title="Add Data"><i class="fas fa-plus"></i> Add</button>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <?= $this->session->flashdata('pesan') ?>
                        <table id="tbl_pengiriman" class="table table-bordered table-striped table-hover">
                            <thead>
                                <tr class="bg-info">
                                    <th>Nomor <PERSON></th>
                                    <th><PERSON>gal</th>
                                    <th>P<PERSON><PERSON><PERSON></th>
                                    <th><PERSON><PERSON></th>
                                    <th>Ekspedisi</th>
                                    <th>Nomor Resi</th>
                                    <th>Status</th>
                                    <th>Total Item</th>
                                    <th>Total Qty</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->

<!-- Modal Form -->
<div class="modal fade" id="modal_form" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <h4 class="modal-title text-white">Form Pengiriman</h4>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" id="btnSave" onclick="save()" class="btn btn-primary">
                    <i class="fa fa-save"></i> Simpan
                </button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail -->
<div class="modal fade" id="modal_detail" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-info">
                <h4 class="modal-title text-white">Detail Pengiriman</h4>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body-detail">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    var table;
    var save_method;
    var id_pengiriman;

    $(document).ready(function() {
        $('.select2').select2();
        
        //datatables
        table = $("#tbl_pengiriman").DataTable({
            "responsive": true,
            "autoWidth": false,
            "language": {
                "sEmptyTable": "Data Pengiriman Belum Ada"
            },
            "processing": true, //Feature control the processing indicator.
            "serverSide": true, //Feature control DataTables' server-side processing mode.
            "order": [], //Initial no order.

            // Load data for the table's content from an Ajax source
            "ajax": {
                "url": "<?php echo site_url('pengiriman/ajax_list') ?>",
                "type": "POST"
            },

            //Set column definition initialisation properties.
            "columnDefs": [{
                "targets": [-1], //last column
                "orderable": false, //set not orderable
            }],
        });
    });

    function add() {
        save_method = 'add';
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('.modal-title').text('Tambah Pengiriman'); // set a title to modal header

        // Load form add
        $.ajax({
            url: "<?php echo site_url('pengiriman/form_input') ?>",
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal-body').html(data);
                $('#modal_form').modal('show'); // show bootstrap modal after loading form
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function edit(id) {
        save_method = 'update';
        id_pengiriman = id;
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('.modal-title').text('Edit Pengiriman'); // set a title to modal header

        // Load form edit
        $.ajax({
            url: "<?php echo site_url('pengiriman/form_input') ?>",
            type: "GET",
            dataType: "HTML",
            success: function(data) {
                $('#modal-body').html(data);
                
                // Load data for edit
                $.ajax({
                    url: "<?php echo site_url('pengiriman/edit/') ?>" + id,
                    type: "GET",
                    dataType: "JSON",
                    success: function(data) {
                        $('[name="id"]').val(data.id);
                        $('[name="nomor_pengiriman"]').val(data.nomor_pengiriman);
                        $('[name="tanggal_pengiriman"]').val(data.tanggal_pengiriman);
                        $('[name="id_pesanan"]').val(data.id_pesanan).trigger('change');
                        $('[name="id_pelanggan"]').val(data.id_pelanggan).trigger('change');
                        $('[name="alamat_pengiriman"]').val(data.alamat_pengiriman);
                        $('[name="ekspedisi"]').val(data.ekspedisi);
                        $('[name="nomor_resi"]').val(data.nomor_resi);
                        $('[name="biaya_pengiriman"]').val(data.biaya_pengiriman);
                        $('[name="status"]').val(data.status).trigger('change');
                        $('[name="estimasi_tiba"]').val(data.estimasi_tiba);
                        $('[name="keterangan"]').val(data.keterangan);
                        
                        // Disable nomor field dan hide tombol generate saat edit
                        $('[name="nomor_pengiriman"]').prop('readonly', true);
                        $('#btn-generate-nomor').hide();
                        
                        $('#modal_form').modal('show'); // show bootstrap modal after loading form
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat memuat data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat memuat form.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        });
    }

    function detail(id) {
        // Redirect to detail page
        window.location.href = "<?php echo site_url('pengiriman/detail/') ?>" + id;
    }

    function save() {
        $('#btnSave').text('Menyimpan...'); //change button text
        $('#btnSave').attr('disabled', true); //set button disable 

        var url;
        if (save_method == 'add') {
            url = "<?php echo site_url('pengiriman/insert') ?>";
        } else {
            url = "<?php echo site_url('pengiriman/update') ?>";
        }

        // ajax adding data to database
        $.ajax({
            url: url,
            type: "POST",
            data: $('#form').serialize(),
            dataType: "JSON",
            success: function(data) {
                if (data.status) //if success close modal and reload ajax table
                {
                    $('#modal_form').modal('hide');
                    table.ajax.reload(null, false);
                    Swal.fire({
                        title: 'Berhasil!',
                        text: 'Data pengiriman berhasil disimpan.',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                } else {
                    for (var i = 0; i < data.inputerror.length; i++) {
                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]); //select span help-block class set text error string
                    }
                }
                $('#btnSave').text('Simpan'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 
            },
            error: function(jqXHR, textStatus, errorThrown) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Terjadi kesalahan saat menyimpan data.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $('#btnSave').text('Simpan'); //change button text
                $('#btnSave').attr('disabled', false); //set button enable 
            }
        });
    }

    function hapus(id) {
        Swal.fire({
            title: 'Konfirmasi',
            text: 'Apakah Anda yakin ingin menghapus data pengiriman ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: "<?php echo site_url('pengiriman/hapus/') ?>" + id,
                    type: "POST",
                    dataType: "JSON",
                    success: function(data) {
                        table.ajax.reload(null, false);
                        Swal.fire({
                            title: 'Berhasil!',
                            text: 'Data pengiriman berhasil dihapus.',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menghapus data.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                });
            }
        });
    }

    function detail_pesanan(id_pesanan) {
        // Function to show pesanan detail - can be implemented later
        Swal.fire({
            title: 'Info',
            text: 'Detail pesanan akan ditampilkan di sini.',
            icon: 'info',
            confirmButtonText: 'OK'
        });
    }
</script>
