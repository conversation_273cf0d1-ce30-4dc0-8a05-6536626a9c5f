-- --------------------------------------------------------
-- Host:                         127.0.0.1
-- Server version:               10.4.27-MariaDB - mariadb.org binary distribution
-- Server OS:                    Win64
-- HeidiSQL Version:             12.10.0.7000
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- Dumping structure for table toko_elektronik.aplikasi
CREATE TABLE IF NOT EXISTS `aplikasi` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `nama_owner` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `alamat` text CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tlp` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `brand` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `title` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `nama_aplikasi` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `logo` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `copy_right` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `versi` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `tahun` year(4) DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `nama_pengirim` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `password` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `npwp` varchar(20) DEFAULT NULL COMMENT 'NPWP Perusahaan',
  `no_telepon` varchar(20) DEFAULT NULL COMMENT 'Telepon Perusahaan',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- Dumping data for table toko_elektronik.aplikasi: ~0 rows (approximately)
INSERT INTO `aplikasi` (`id`, `nama_owner`, `alamat`, `tlp`, `brand`, `title`, `nama_aplikasi`, `logo`, `copy_right`, `versi`, `tahun`, `email`, `nama_pengirim`, `password`, `npwp`, `no_telepon`) VALUES
	(1, 'PT. FOE', 'jalan raya', '085838333009', NULL, 'Toko Elektronik', 'Toko Elektronik', 'Logo.png', 'Copyright ©', '*******', '2025', '<EMAIL>', 'Aryo Coding', 'pfpinffqxutdjexq', '01.234.567.8-901.000', '021-12345678');

-- Dumping structure for table toko_elektronik.barang
CREATE TABLE IF NOT EXISTS `barang` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_barang` varchar(50) NOT NULL,
  `nama_barang` varchar(255) NOT NULL,
  `merk` varchar(100) DEFAULT NULL,
  `tipe` varchar(100) DEFAULT NULL,
  `spesifikasi` text DEFAULT NULL,
  `satuan_id` int(11) DEFAULT NULL,
  `jenis_pajak_id` int(11) DEFAULT NULL,
  `stok_minimum` int(11) DEFAULT 0,
  `harga_beli` decimal(18,2) NOT NULL,
  `harga_jual` decimal(18,2) DEFAULT NULL,
  `aktif` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_barang` (`kode_barang`),
  KEY `fk_barang_satuan` (`satuan_id`),
  KEY `fk_barang_jenis_pajak` (`jenis_pajak_id`),
  CONSTRAINT `fk_barang_jenis_pajak` FOREIGN KEY (`jenis_pajak_id`) REFERENCES `jenis_pajak` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `fk_barang_satuan` FOREIGN KEY (`satuan_id`) REFERENCES `satuan` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.barang: ~10 rows (approximately)
INSERT INTO `barang` (`id`, `kode_barang`, `nama_barang`, `merk`, `tipe`, `spesifikasi`, `satuan_id`, `jenis_pajak_id`, `stok_minimum`, `harga_beli`, `harga_jual`, `aktif`, `created_at`, `updated_at`) VALUES
	(1, 'BRG0001', 'Smartphone Samsung Galaxy A54 5G', 'Samsung', 'Galaxy A54 5G', 'RAM 8GB, Storage 256GB, Camera 50MP, Battery 5000mAh, Display 6.4" Super AMOLED', 1, 1, 5, 4500000.00, 5200000.00, 1, '2025-05-29 07:29:25', '2025-05-29 08:53:30'),
	(2, 'BRG0002', 'Laptop ASUS VivoBook 14', 'ASUS', 'VivoBook 14 X1404VA', 'Intel Core i5-1335U, RAM 8GB DDR4, SSD 512GB, Intel Iris Xe Graphics, 14" FHD IPS', 1, 1, 3, 7800000.00, 8900000.00, 1, '2025-05-29 07:29:25', '2025-05-29 08:53:30'),
	(3, 'BRG0003', 'Smart TV LG 43" 4K UHD', 'LG', '43UP7750PTB', '43 inch 4K UHD, WebOS Smart TV, HDR10 Pro, AI ThinQ, Magic Remote', 1, 1, 2, 4200000.00, 4800000.00, 1, '2025-05-29 07:29:25', '2025-05-29 08:53:30'),
	(4, 'BRG0004', 'Headphone Sony WH-1000XM4', 'Sony', 'WH-1000XM4', 'Wireless Noise Canceling, 30 Hours Battery, Quick Charge, Touch Control', 1, 1, 10, 3200000.00, 3800000.00, 1, '2025-05-29 07:29:25', '2025-05-29 08:53:30'),
	(5, 'BRG0005', 'Kamera Canon EOS M50 Mark II', 'Canon', 'EOS M50 Mark II', '24.1MP APS-C CMOS, 4K Video, Dual Pixel CMOS AF, WiFi, Bluetooth', 1, 1, 2, 8500000.00, 9800000.00, 1, '2025-05-29 07:29:25', '2025-05-29 08:53:30'),
	(6, 'BRG0006', 'Tablet iPad Air 5th Gen', 'Apple', 'iPad Air 5th Generation', 'M1 Chip, 10.9" Liquid Retina Display, 64GB WiFi, Touch ID, Apple Pencil Support', 1, 1, 3, 7200000.00, 8500000.00, 1, '2025-05-29 07:29:25', '2025-05-29 08:53:30'),
	(7, 'BRG0007', 'Smartwatch Apple Watch Series 8', 'Apple', 'Watch Series 8 GPS', '45mm Aluminum Case, Sport Band, Blood Oxygen, ECG, Always-On Retina Display', 1, 1, 5, 5800000.00, 6800000.00, 1, '2025-05-29 07:29:25', '2025-05-29 08:53:30'),
	(8, 'BRG0008', 'Speaker JBL Charge 5', 'JBL', 'Charge 5', 'Portable Bluetooth Speaker, IP67 Waterproof, 20 Hours Playtime, PowerBank Function', 1, 1, 8, 1800000.00, 2200000.00, 1, '2025-05-29 07:29:25', '2025-05-29 08:53:30'),
	(9, 'BRG0009', 'Gaming Mouse Logitech G Pro X', 'Logitech', 'G Pro X Superlight', 'Wireless Gaming Mouse, 25K DPI HERO Sensor, 70g Ultra-lightweight, 70 Hours Battery', 1, 1, 15, 1400000.00, 1750000.00, 1, '2025-05-29 07:29:25', '2025-05-29 08:53:30'),
	(10, 'BRG0010', 'Power Bank Xiaomi 20000mAh', 'Xiaomi', 'Mi Power Bank 3 Pro', '20000mAh Capacity, 50W Fast Charging, USB-C PD, Dual USB-A, LED Display', 1, 1, 20, 350000.00, 450000.00, 1, '2025-05-29 07:29:25', '2025-05-29 08:53:30');

-- Dumping structure for table toko_elektronik.barang_keluar
CREATE TABLE IF NOT EXISTS `barang_keluar` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `nomor_pengeluaran` varchar(50) NOT NULL,
  `tanggal` date NOT NULL,
  `id_pelanggan` int(11) DEFAULT NULL,
  `jenis` enum('penjualan','retur_pembelian','transfer_keluar','penyesuaian','produksi','rusak','hilang','sample') NOT NULL,
  `ref_nomor` varchar(50) DEFAULT NULL,
  `keterangan` text DEFAULT NULL,
  `status` enum('draft','final') DEFAULT 'draft',
  `total_item` int(11) DEFAULT 0,
  `total_qty` decimal(15,2) DEFAULT 0.00,
  `created_by` int(11) DEFAULT NULL,
  `finalized_by` int(11) DEFAULT NULL,
  `finalized_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_pengeluaran` (`nomor_pengeluaran`),
  KEY `id_pelanggan` (`id_pelanggan`),
  KEY `idx_barang_keluar_tanggal` (`tanggal`),
  KEY `idx_barang_keluar_jenis` (`jenis`),
  KEY `idx_barang_keluar_status` (`status`),
  KEY `idx_barang_keluar_nomor` (`nomor_pengeluaran`),
  KEY `idx_barang_keluar_jenis_ref` (`jenis`,`ref_nomor`),
  CONSTRAINT `barang_keluar_ibfk_1` FOREIGN KEY (`id_pelanggan`) REFERENCES `pelanggan` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.barang_keluar: ~4 rows (approximately)
INSERT INTO `barang_keluar` (`id`, `nomor_pengeluaran`, `tanggal`, `id_pelanggan`, `jenis`, `ref_nomor`, `keterangan`, `status`, `total_item`, `total_qty`, `created_by`, `finalized_by`, `finalized_at`, `created_at`, `updated_at`) VALUES
	(1, 'BK-2025-06-001', '2025-06-01', NULL, 'rusak', 'REF001', 'Barang Ruak Kena Banjir', 'final', 1, 1.00, 1, 1, '2025-06-01 15:12:47', '2025-06-01 08:09:48', '2025-06-01 08:12:47'),
	(7, 'TK-20250602-0006', '2025-06-02', NULL, 'transfer_keluar', 'TR-2025-06-003', 'Transfer ke Gudang Cabang Surabaya', 'final', 1, 1.00, 1, 1, '2025-06-02 17:44:49', '2025-06-02 10:44:49', '2025-06-02 10:44:49'),
	(8, 'TK-20250602-0007', '2025-06-02', NULL, 'transfer_keluar', 'TR-2025-06-004', 'Transfer ke Gudang Cabang Surabaya', 'final', 1, 1.00, 1, 1, '2025-06-02 20:51:00', '2025-06-02 13:51:00', '2025-06-02 13:51:00'),
	(9, 'BK-2025-06-002', '2025-06-07', 3, 'penjualan', '11', '1', 'final', 1, 1.00, 1, 1, '2025-06-07 22:04:56', '2025-06-07 14:07:36', '2025-06-07 15:04:56'),
	(10, 'BK-2025-06-003', '2025-06-07', 3, 'penjualan', '111', '11', 'draft', 1, 1.00, 1, NULL, NULL, '2025-06-07 23:08:37', '2025-06-07 23:09:15');

-- Dumping structure for table toko_elektronik.barang_keluar_detail
CREATE TABLE IF NOT EXISTS `barang_keluar_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `id_barang_keluar` bigint(20) unsigned NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty_keluar` decimal(12,2) NOT NULL DEFAULT 0.00,
  `id_satuan` int(11) DEFAULT NULL,
  `harga_satuan` decimal(15,2) DEFAULT 0.00,
  `total_harga` decimal(15,2) GENERATED ALWAYS AS (`qty_keluar` * `harga_satuan`) STORED,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `id_barang_keluar` (`id_barang_keluar`),
  KEY `id_satuan` (`id_satuan`),
  KEY `idx_barang_keluar_detail_barang` (`id_barang`),
  KEY `idx_barang_keluar_detail_gudang` (`id_gudang`),
  CONSTRAINT `barang_keluar_detail_ibfk_1` FOREIGN KEY (`id_barang_keluar`) REFERENCES `barang_keluar` (`id`) ON DELETE CASCADE,
  CONSTRAINT `barang_keluar_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`),
  CONSTRAINT `barang_keluar_detail_ibfk_3` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`),
  CONSTRAINT `barang_keluar_detail_ibfk_4` FOREIGN KEY (`id_satuan`) REFERENCES `satuan` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.barang_keluar_detail: ~5 rows (approximately)
INSERT INTO `barang_keluar_detail` (`id`, `id_barang_keluar`, `id_barang`, `id_gudang`, `qty_keluar`, `id_satuan`, `harga_satuan`, `keterangan`, `created_at`, `updated_at`) VALUES
	(1, 1, 2, 4, 1.00, 1, 8900000.00, '', '2025-06-01 08:10:26', '2025-06-01 08:10:26'),
	(7, 7, 2, 4, 1.00, 1, 0.00, 'Transfer ke gudang Gudang Cabang Surabaya - 1', '2025-06-02 10:44:49', '2025-06-02 10:44:49'),
	(8, 8, 3, 4, 1.00, 1, 0.00, 'Transfer ke gudang Gudang Cabang Surabaya - 1', '2025-06-02 13:51:00', '2025-06-02 13:51:00'),
	(11, 9, 9, 5, 1.00, 1, 1750000.00, '', '2025-06-07 14:53:33', '2025-06-07 14:53:33'),
	(12, 10, 2, 4, 1.00, 1, 8900000.00, '', '2025-06-07 23:09:15', '2025-06-07 23:09:15');

-- Dumping structure for table toko_elektronik.barang_masuk
CREATE TABLE IF NOT EXISTS `barang_masuk` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `nomor_penerimaan` varchar(50) NOT NULL,
  `tanggal` date NOT NULL,
  `id_supplier` int(11) DEFAULT NULL,
  `jenis` enum('pembelian','retur_penjualan','bonus','titipan','penyesuaian','produksi','transfer_masuk') NOT NULL,
  `ref_nomor` varchar(50) DEFAULT NULL,
  `keterangan` text DEFAULT NULL,
  `status` enum('draft','final') DEFAULT 'draft',
  `total_item` int(11) DEFAULT 0,
  `total_qty` decimal(15,2) DEFAULT 0.00,
  `created_by` int(11) DEFAULT NULL,
  `finalized_by` int(11) DEFAULT NULL,
  `finalized_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_penerimaan` (`nomor_penerimaan`),
  KEY `created_by` (`created_by`),
  KEY `finalized_by` (`finalized_by`),
  KEY `idx_barang_masuk_tanggal` (`tanggal`),
  KEY `idx_barang_masuk_supplier` (`id_supplier`),
  KEY `idx_barang_masuk_jenis` (`jenis`),
  KEY `idx_barang_masuk_status` (`status`),
  KEY `idx_barang_masuk_nomor` (`nomor_penerimaan`),
  KEY `idx_barang_masuk_jenis_ref` (`jenis`,`ref_nomor`),
  CONSTRAINT `barang_masuk_ibfk_1` FOREIGN KEY (`id_supplier`) REFERENCES `supplier` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `barang_masuk_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `barang_masuk_ibfk_3` FOREIGN KEY (`finalized_by`) REFERENCES `tbl_user` (`id_user`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.barang_masuk: ~3 rows (approximately)
INSERT INTO `barang_masuk` (`id`, `nomor_penerimaan`, `tanggal`, `id_supplier`, `jenis`, `ref_nomor`, `keterangan`, `status`, `total_item`, `total_qty`, `created_by`, `finalized_by`, `finalized_at`, `created_at`, `updated_at`) VALUES
	(1, 'BM-2025-001', '2025-01-15', 1, 'pembelian', 'PO-2025-001', 'Pembelian barang elektronik bulan Januari', 'final', 2, 2.00, 1, 1, '2025-06-07 21:45:22', '2025-06-01 06:15:22', '2025-06-07 14:45:22'),
	(2, 'TM-20250602-0006', '2025-06-02', NULL, 'transfer_masuk', 'TR-2025-06-003', 'Transfer dari Gudang Utama', 'final', 1, 1.00, 1, 1, '2025-06-02 20:48:04', '2025-06-02 13:48:04', '2025-06-02 13:48:04'),
	(3, 'TM-20250602-0007', '2025-06-02', NULL, 'transfer_masuk', 'TR-2025-06-004', 'Transfer dari Gudang Utama', 'final', 1, 1.00, 1, 1, '2025-06-02 20:51:11', '2025-06-02 13:51:11', '2025-06-02 13:51:11'),
	(4, 'BM-2025-002', '2025-06-07', 2, 'retur_penjualan', '111', '', 'draft', 1, 2.00, 1, NULL, NULL, '2025-06-07 23:07:06', '2025-06-07 23:07:32');

-- Dumping structure for table toko_elektronik.barang_masuk_detail
CREATE TABLE IF NOT EXISTS `barang_masuk_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `id_barang_masuk` bigint(20) unsigned NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty_diterima` decimal(12,2) NOT NULL DEFAULT 0.00,
  `id_satuan` int(11) DEFAULT NULL,
  `harga_satuan` decimal(15,2) DEFAULT 0.00,
  `total_harga` decimal(15,2) GENERATED ALWAYS AS (`qty_diterima` * `harga_satuan`) STORED,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_barang_masuk_barang_gudang` (`id_barang_masuk`,`id_barang`,`id_gudang`),
  KEY `id_satuan` (`id_satuan`),
  KEY `idx_barang_masuk_detail_barang_masuk` (`id_barang_masuk`),
  KEY `idx_barang_masuk_detail_barang` (`id_barang`),
  KEY `idx_barang_masuk_detail_gudang` (`id_gudang`),
  CONSTRAINT `barang_masuk_detail_ibfk_1` FOREIGN KEY (`id_barang_masuk`) REFERENCES `barang_masuk` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `barang_masuk_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `barang_masuk_detail_ibfk_3` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `barang_masuk_detail_ibfk_4` FOREIGN KEY (`id_satuan`) REFERENCES `satuan` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.barang_masuk_detail: ~5 rows (approximately)
INSERT INTO `barang_masuk_detail` (`id`, `id_barang_masuk`, `id_barang`, `id_gudang`, `qty_diterima`, `id_satuan`, `harga_satuan`, `keterangan`, `created_at`, `updated_at`) VALUES
	(1, 2, 2, 5, 1.00, 1, 0.00, 'Transfer dari gudang Gudang Utama - 1', '2025-06-02 13:48:04', '2025-06-02 13:48:04'),
	(2, 3, 3, 5, 1.00, 1, 0.00, 'Transfer dari gudang Gudang Utama - 1', '2025-06-02 13:51:11', '2025-06-02 13:51:11'),
	(3, 1, 9, 5, 1.00, 1, 1400000.00, '1', '2025-06-07 13:40:04', '2025-06-07 13:40:04'),
	(8, 1, 9, 6, 1.00, 1, 1400000.00, '1', '2025-06-07 13:57:23', '2025-06-07 14:04:53'),
	(11, 4, 9, 5, 2.00, 1, 1400000.00, '', '2025-06-07 23:07:23', '2025-06-07 23:07:32');

-- Dumping structure for table toko_elektronik.ekspedisi
CREATE TABLE IF NOT EXISTS `ekspedisi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_ekspedisi` varchar(20) NOT NULL,
  `nama_ekspedisi` varchar(100) NOT NULL,
  `website` varchar(100) DEFAULT NULL,
  `telepon` varchar(20) DEFAULT NULL,
  `tarif_per_kg` decimal(10,2) DEFAULT 0.00,
  `aktif` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_ekspedisi` (`kode_ekspedisi`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.ekspedisi: ~7 rows (approximately)
INSERT INTO `ekspedisi` (`id`, `kode_ekspedisi`, `nama_ekspedisi`, `website`, `telepon`, `tarif_per_kg`, `aktif`) VALUES
	(1, 'JNE', 'JNE Express', 'www.jne.co.id', '021-2927-8888', 15000.00, 1),
	(2, 'SCP', 'Sicepat Express', 'www.sicepat.com', '021-5020-0050', 12000.00, 1),
	(3, 'JNT', 'J&T Express', 'www.jet.co.id', '021-8066-0888', 13000.00, 1),
	(4, 'POS', 'Pos Indonesia', 'www.posindonesia.co.id', '161', 10000.00, 1),
	(5, 'ANT', 'Anteraja', 'www.anteraja.id', '021-5060-3030', 14000.00, 1),
	(6, 'TKI', 'Tiki', 'www.tiki.id', '1500-125', 16000.00, 1),
	(7, 'LNX', 'Lion Parcel', 'www.lionparcel.com', '021-6379-8888', 11000.00, 1);

-- Dumping structure for table toko_elektronik.error_log
CREATE TABLE IF NOT EXISTS `error_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `table_name` varchar(100) NOT NULL,
  `error_message` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_table_name` (`table_name`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.error_log: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.faktur_penjualan
CREATE TABLE IF NOT EXISTS `faktur_penjualan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nomor_faktur` varchar(50) NOT NULL,
  `tanggal_faktur` date NOT NULL,
  `tanggal_jatuh_tempo` date DEFAULT NULL,
  `id_pelanggan` int(11) NOT NULL,
  `id_pesanan` int(11) DEFAULT NULL,
  `id_pengiriman` int(11) DEFAULT NULL,
  `jenis_faktur` enum('reguler','pajak') NOT NULL DEFAULT 'reguler',
  `status` enum('draft','diterbitkan','dibayar','overdue','dibatalkan') NOT NULL DEFAULT 'draft',
  `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00,
  `diskon_persen` decimal(5,2) DEFAULT 0.00,
  `diskon_nominal` decimal(15,2) DEFAULT 0.00,
  `total_sebelum_pajak` decimal(15,2) NOT NULL DEFAULT 0.00,
  `ppn_persen` decimal(5,2) DEFAULT 11.00,
  `ppn_nominal` decimal(15,2) DEFAULT 0.00,
  `total_setelah_pajak` decimal(15,2) NOT NULL DEFAULT 0.00,
  `total_item` int(11) NOT NULL DEFAULT 0,
  `total_qty` decimal(10,2) NOT NULL DEFAULT 0.00,
  `keterangan` text DEFAULT NULL,
  `alamat_penagihan` text DEFAULT NULL,
  `syarat_pembayaran` varchar(100) DEFAULT NULL,
  `metode_pembayaran` enum('tunai','transfer','kredit','cek','giro') DEFAULT 'tunai',
  `nomor_po_pelanggan` varchar(50) DEFAULT NULL,
  `sales_person` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` varchar(50) DEFAULT NULL,
  `updated_by` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_faktur` (`nomor_faktur`),
  KEY `id_pelanggan` (`id_pelanggan`),
  KEY `id_pesanan` (`id_pesanan`),
  KEY `id_pengiriman` (`id_pengiriman`),
  KEY `idx_faktur_tanggal` (`tanggal_faktur`),
  KEY `idx_faktur_status` (`status`),
  KEY `idx_faktur_jenis` (`jenis_faktur`),
  KEY `idx_faktur_created_at` (`created_at`),
  CONSTRAINT `faktur_penjualan_ibfk_1` FOREIGN KEY (`id_pelanggan`) REFERENCES `pelanggan` (`id`),
  CONSTRAINT `faktur_penjualan_ibfk_2` FOREIGN KEY (`id_pesanan`) REFERENCES `pesanan` (`id`),
  CONSTRAINT `faktur_penjualan_ibfk_3` FOREIGN KEY (`id_pengiriman`) REFERENCES `pengiriman` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.faktur_penjualan: ~0 rows (approximately)
INSERT INTO `faktur_penjualan` (`id`, `nomor_faktur`, `tanggal_faktur`, `tanggal_jatuh_tempo`, `id_pelanggan`, `id_pesanan`, `id_pengiriman`, `jenis_faktur`, `status`, `subtotal`, `diskon_persen`, `diskon_nominal`, `total_sebelum_pajak`, `ppn_persen`, `ppn_nominal`, `total_setelah_pajak`, `total_item`, `total_qty`, `keterangan`, `alamat_penagihan`, `syarat_pembayaran`, `metode_pembayaran`, `nomor_po_pelanggan`, `sales_person`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
	(1, 'FP-2025-01-0001', '2025-01-15', '2025-02-14', 1, NULL, NULL, 'reguler', 'draft', 0.00, 0.00, 0.00, 0.00, 11.00, 0.00, 0.00, 0, 0.00, NULL, NULL, 'NET 30', 'tunai', NULL, NULL, '2025-06-06 06:34:57', '2025-06-06 06:34:57', 'admin', NULL);

-- Dumping structure for table toko_elektronik.faktur_penjualan_detail
CREATE TABLE IF NOT EXISTS `faktur_penjualan_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_faktur` int(11) NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty` decimal(10,2) NOT NULL,
  `harga_satuan` decimal(15,2) NOT NULL,
  `diskon_persen` decimal(5,2) DEFAULT 0.00,
  `diskon_nominal` decimal(15,2) DEFAULT 0.00,
  `subtotal_sebelum_diskon` decimal(15,2) NOT NULL,
  `subtotal_setelah_diskon` decimal(15,2) NOT NULL,
  `ppn_persen` decimal(5,2) DEFAULT 11.00,
  `ppn_nominal` decimal(15,2) DEFAULT 0.00,
  `total_akhir` decimal(15,2) NOT NULL,
  `keterangan` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_faktur` (`id_faktur`),
  KEY `id_barang` (`id_barang`),
  KEY `id_gudang` (`id_gudang`),
  KEY `idx_faktur_detail_qty` (`qty`),
  KEY `idx_faktur_detail_total` (`total_akhir`),
  CONSTRAINT `faktur_penjualan_detail_ibfk_1` FOREIGN KEY (`id_faktur`) REFERENCES `faktur_penjualan` (`id`) ON DELETE CASCADE,
  CONSTRAINT `faktur_penjualan_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`),
  CONSTRAINT `faktur_penjualan_detail_ibfk_3` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.faktur_penjualan_detail: ~0 rows (approximately)

-- Dumping structure for table toko_elektronik.gudang
CREATE TABLE IF NOT EXISTS `gudang` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_gudang` varchar(50) DEFAULT NULL,
  `nama_gudang` varchar(100) NOT NULL,
  `alamat` text DEFAULT NULL,
  `penanggung_jawab` varchar(100) DEFAULT NULL,
  `no_telp` varchar(30) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `keterangan` text DEFAULT NULL,
  `aktif` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_gudang` (`kode_gudang`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.gudang: ~7 rows (approximately)
INSERT INTO `gudang` (`id`, `kode_gudang`, `nama_gudang`, `alamat`, `penanggung_jawab`, `no_telp`, `email`, `keterangan`, `aktif`, `created_at`, `updated_at`) VALUES
	(1, 'GD001', 'Gudang Utama', 'Jl. Raya Industri No. 123, Jakarta', NULL, NULL, NULL, NULL, 1, '2025-06-08 14:29:05', '2025-06-08 14:29:05'),
	(2, 'GD002', 'Gudang Cabang', 'Jl. Sudirman No. 456, Bandung', NULL, NULL, NULL, NULL, 1, '2025-06-08 14:29:05', '2025-06-08 14:29:05'),
	(3, 'GDG0003', 'Gudang Transit Bandung', 'Jl. Soekarno Hatta No. 789, Bandung', 'Ahmad Wijaya', '022-9876543', '<EMAIL>', 'Gudang transit untuk distribusi wilayah Jawa Barat', 1, '2025-05-29 09:07:02', '2025-05-29 09:07:02'),
	(4, 'GDG0001', 'Gudang Utama', 'Jl. Raya Industri No. 123, Jakarta Timur', 'Budi Santoso', '021-8765432', '<EMAIL>', 'Gudang utama untuk penyimpanan barang elektronik', 1, '2025-05-29 09:07:02', '2025-05-29 09:07:02'),
	(5, 'GDG0002', 'Gudang Cabang Surabaya', 'Jl. Ahmad Yani No. 456, Surabaya', 'Siti Rahayu', '031-7654321', '<EMAIL>', 'Gudang cabang untuk wilayah Jawa Timur', 1, '2025-05-29 09:07:02', '2025-05-29 09:07:02'),
	(6, 'GDG0004', 'Gudang Spare Parts', 'Jl. Gatot Subroto No. 321, Jakarta Selatan', 'Dewi Lestari', '021-5432109', '<EMAIL>', 'Gudang khusus untuk spare parts dan aksesoris', 1, '2025-05-29 09:07:02', '2025-05-29 09:07:02'),
	(7, 'GDG0005', 'Gudang Return', 'Jl. MT Haryono No. 654, Jakarta Timur', 'Rudi Hermawan', '021-2109876', 'rudi@tokoelek', 'Gudang untuk barang return dan refurbish', 0, '2025-05-29 09:07:02', '2025-05-30 19:55:17');

-- Dumping structure for table toko_elektronik.jenis_pajak
CREATE TABLE IF NOT EXISTS `jenis_pajak` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_pajak` varchar(50) NOT NULL,
  `nama_pajak` varchar(100) NOT NULL,
  `tarif_persen` decimal(5,2) NOT NULL COMMENT 'Persentase potongan pajak, contoh 10.00 untuk 10%',
  `tanggal_mulai` date NOT NULL,
  `tanggal_selesai` date DEFAULT NULL COMMENT 'Boleh NULL jika masa berakhir belum ditentukan',
  `keterangan` text DEFAULT NULL,
  `aktif` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_pajak` (`kode_pajak`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.jenis_pajak: ~4 rows (approximately)
INSERT INTO `jenis_pajak` (`id`, `kode_pajak`, `nama_pajak`, `tarif_persen`, `tanggal_mulai`, `tanggal_selesai`, `keterangan`, `aktif`, `created_at`, `updated_at`) VALUES
	(1, 'PJK0001', 'PPN (Pajak Pertambahan Nilai)', 11.00, '2022-04-01', NULL, 'Pajak Pertambahan Nilai sesuai UU No. 7 Tahun 2021', 1, '2025-05-29 07:56:28', '2025-05-29 07:56:28'),
	(2, 'PJK0002', 'PPh 21', 5.00, '2021-01-01', NULL, 'Pajak Penghasilan Pasal 21 untuk karyawan', 1, '2025-05-29 07:56:28', '2025-05-29 07:56:28'),
	(3, 'PJK0003', 'PPh 23', 2.00, '2021-01-01', NULL, 'Pajak Penghasilan Pasal 23 untuk jasa', 1, '2025-05-29 07:56:28', '2025-05-29 07:56:28'),
	(4, 'PJK0004', 'PPN Lama', 10.00, '2020-01-01', '2022-03-31', 'PPN tarif lama sebelum kenaikan', 0, '2025-05-29 07:56:28', '2025-05-29 07:56:28');

-- Dumping structure for table toko_elektronik.pelanggan
CREATE TABLE IF NOT EXISTS `pelanggan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode` varchar(50) NOT NULL,
  `nama` varchar(255) NOT NULL,
  `no_telepon` varchar(255) NOT NULL,
  `alamat` text NOT NULL,
  `alamat_kirim` text DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `nama_pic` varchar(100) DEFAULT NULL,
  `telepon_pic` varchar(20) DEFAULT NULL,
  `npwp` varchar(30) DEFAULT NULL,
  `no_ktp` varchar(30) DEFAULT NULL,
  `is_pkp` tinyint(1) DEFAULT 0,
  `status_aktif` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode` (`kode`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.pelanggan: ~10 rows (approximately)
INSERT INTO `pelanggan` (`id`, `kode`, `nama`, `no_telepon`, `alamat`, `alamat_kirim`, `email`, `nama_pic`, `telepon_pic`, `npwp`, `no_ktp`, `is_pkp`, `status_aktif`, `created_at`, `updated_at`) VALUES
	(1, 'PLG0001', 'PT Elektronik Jaya', '081234567890', 'Jl. Sudirman No. 123, Tanah Abang, Jakarta Pusat 10270', 'Jl. Sudirman No. 123, Tanah Abang, Jakarta Pusat 10270', '<EMAIL>', 'Budi Santoso', '081234567891', '01.234.567.8-901.000', '3171012345678901', 1, 1, '2025-05-29 13:38:00', '2025-05-31 03:21:22'),
	(2, 'PLG0002', 'PT Mitra Teknologi Nusantara', '081345678901', 'Jl. Gatot Subroto Kav. 45, Setiabudi, Jakarta Selatan 12930', 'Jl. Gatot Subroto Kav. 45, Setiabudi, Jakarta Selatan 12930', '<EMAIL>', 'Sari Dewi', '081345678902', '01.345.678.9-012.000', '3172023456789012', 1, 1, '2025-05-29 13:43:45', '2025-05-31 03:21:22'),
	(3, 'PLG0003', 'PT Digital Solutions Indonesia', '081456789012', 'Jl. HR Rasuna Said Blok X-5 Kav. 1-2, Kuningan, Jakarta Selatan 12950', 'Jl. HR Rasuna Said Blok X-5 Kav. 1-2, Kuningan, Jakarta Selatan 12950', '<EMAIL>', 'Ahmad Rizki', '081456789013', '01.456.789.0-123.000', '3173034567890123', 1, 1, '2025-05-29 13:43:45', '2025-05-31 03:21:22'),
	(4, 'PLG0004', 'PT Elektronik Prima Tangerang', '081567890123', 'Jl. Jenderal Sudirman No. 88, Tangerang Kota, Banten 15111', 'Jl. Jenderal Sudirman No. 88, Tangerang Kota, Banten 15111', '<EMAIL>', 'Linda Maharani', '081567890124', '01.567.890.1-234.000', '3671045678901234', 1, 1, '2025-05-29 13:43:45', '2025-05-31 03:21:22'),
	(5, 'PLG0005', 'PT Teknologi Canggih Serpong', '081678901234', 'Jl. BSD Raya Utama Sektor VII, BSD City, Tangerang Selatan 15345', 'Jl. BSD Raya Utama Sektor VII, BSD City, Tangerang Selatan 15345', '<EMAIL>', 'Rudi Hermawan', '081678901235', '01.678.901.2-345.000', '3674056789012345', 1, 1, '2025-05-29 13:43:45', '2025-05-31 03:21:22'),
	(6, 'PLG0006', 'PT Komputer Nusantara', '081789012345', 'Jl. Thamrin No. 59, Menteng, Jakarta Pusat 10350', 'Jl. Thamrin No. 59, Menteng, Jakarta Pusat 10350', '<EMAIL>', 'Dewi Sartika', '081789012346', '01.789.012.3-456.000', '3171067890123456', 1, 1, '2025-05-29 13:43:45', '2025-05-31 03:21:22'),
	(7, 'PLG0007', 'PT Elektronik Mandiri Jakarta', '081890123456', 'Jl. Casablanca Raya Kav. 88, Tebet, Jakarta Selatan 12870', 'Jl. Casablanca Raya Kav. 88, Tebet, Jakarta Selatan 12870', '<EMAIL>', 'Andi Wijaya', '081890123457', '01.890.123.4-567.000', '3175078901234567', 1, 1, '2025-05-29 13:43:45', '2025-05-31 03:21:22'),
	(8, 'PLG0008', 'PT Gadget Center Indonesia', '081901234567', 'Jl. Gajah Mada No. 188, Taman Sari, Jakarta Barat 11120', 'Jl. Gajah Mada No. 188, Taman Sari, Jakarta Barat 11120', '<EMAIL>', 'Maya Sari', '081901234568', '01.901.234.5-678.000', '3174089012345678', 1, 1, '2025-05-29 13:43:45', '2025-05-31 03:21:22'),
	(9, 'PLG0009', 'PT Smart Technology Bintaro', '082012345678', 'Jl. Bintaro Utama Sektor 3A, Bintaro Jaya, Tangerang Selatan 15224', 'Jl. Bintaro Utama Sektor 3A, Bintaro Jaya, Tangerang Selatan 15224', '<EMAIL>', 'Hendra Kusuma', '082012345679', '02.012.345.6-789.000', '3674090123456789', 1, 1, '2025-05-29 13:43:45', '2025-05-31 03:21:22'),
	(10, 'PLG0010', 'PT Elektronik Terpadu Kelapa Gading', '082123456789', 'Jl. Boulevard Raya Blok RA No. 15, Kelapa Gading, Jakarta Utara 14240', 'Jl. Boulevard Raya Blok RA No. 15, Kelapa Gading, Jakarta Utara 14240', '<EMAIL>', 'Fitri Handayani', '082123456790', '02.123.456.7-890.000', '3175101234567890', 1, 1, '2025-05-29 13:43:45', '2025-05-31 03:21:22');

-- Dumping structure for table toko_elektronik.pembelian
CREATE TABLE IF NOT EXISTS `pembelian` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nomor_pembelian` varchar(50) NOT NULL,
  `tanggal_pembelian` date NOT NULL,
  `id_supplier` int(11) NOT NULL,
  `jenis_pembelian` enum('reguler','konsinyasi','kontrak') NOT NULL DEFAULT 'reguler',
  `status` enum('draft','disetujui','dipesan','diterima','selesai','dibatalkan') NOT NULL DEFAULT 'draft',
  `total_item` int(11) NOT NULL DEFAULT 0,
  `total_qty` decimal(10,2) NOT NULL DEFAULT 0.00,
  `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00,
  `diskon_persen` decimal(5,2) DEFAULT 0.00,
  `diskon_nominal` decimal(15,2) DEFAULT 0.00,
  `total_sebelum_pajak` decimal(15,2) NOT NULL DEFAULT 0.00,
  `ppn_persen` decimal(5,2) DEFAULT 11.00,
  `ppn_nominal` decimal(15,2) DEFAULT 0.00,
  `total_setelah_pajak` decimal(15,2) NOT NULL DEFAULT 0.00,
  `biaya_pengiriman` decimal(15,2) DEFAULT 0.00,
  `total_akhir` decimal(15,2) NOT NULL DEFAULT 0.00,
  `tanggal_jatuh_tempo` date DEFAULT NULL,
  `syarat_pembayaran` varchar(100) DEFAULT NULL,
  `metode_pembayaran` enum('tunai','transfer','kredit','cek','giro') DEFAULT 'transfer',
  `nomor_po_supplier` varchar(50) DEFAULT NULL,
  `alamat_pengiriman` text DEFAULT NULL,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` varchar(50) DEFAULT NULL,
  `updated_by` varchar(50) DEFAULT NULL,
  `approved_by` varchar(50) DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_pembelian` (`nomor_pembelian`),
  KEY `id_supplier` (`id_supplier`),
  KEY `idx_pembelian_tanggal` (`tanggal_pembelian`),
  KEY `idx_pembelian_status` (`status`),
  KEY `idx_pembelian_jenis` (`jenis_pembelian`),
  CONSTRAINT `pembelian_ibfk_1` FOREIGN KEY (`id_supplier`) REFERENCES `supplier` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.pembelian: ~3 rows (approximately)
INSERT INTO `pembelian` (`id`, `nomor_pembelian`, `tanggal_pembelian`, `id_supplier`, `jenis_pembelian`, `status`, `total_item`, `total_qty`, `subtotal`, `diskon_persen`, `diskon_nominal`, `total_sebelum_pajak`, `ppn_persen`, `ppn_nominal`, `total_setelah_pajak`, `biaya_pengiriman`, `total_akhir`, `tanggal_jatuh_tempo`, `syarat_pembayaran`, `metode_pembayaran`, `nomor_po_supplier`, `alamat_pengiriman`, `keterangan`, `created_at`, `updated_at`, `created_by`, `updated_by`, `approved_by`, `approved_at`) VALUES
	(1, 'PB-20250101-0001', '2025-01-01', 1, 'reguler', 'draft', 2, 15.00, 84000000.00, 0.00, 0.00, 84000000.00, 11.00, 9240000.00, 93240000.00, 0.00, 93240000.00, NULL, 'NET 30', 'transfer', NULL, 'Jl. Raya Industri No. 123, Jakarta Timur', 'Pembelian smartphone dan laptop untuk stok awal tahun', '2025-06-06 09:57:01', '2025-06-06 09:57:01', 'admin', NULL, NULL, NULL),
	(2, 'PB-********-0002', '2025-01-02', 2, 'reguler', 'diterima', 2, 8.00, 28600000.00, 0.00, 0.00, 28600000.00, 11.00, 3146000.00, 31746000.00, 0.00, 31746000.00, NULL, 'NET 15', 'transfer', NULL, 'Jl. Raya Industri No. 123, Jakarta Timur', 'Pembelian peralatan elektronik untuk meeting room', '2025-06-06 09:57:01', '2025-06-07 12:10:37', 'admin', NULL, NULL, NULL),
	(3, 'PB-20250103-0003', '2025-01-03', 3, 'konsinyasi', 'dipesan', 1, 10.00, 18000000.00, 0.00, 0.00, 18000000.00, 11.00, 1980000.00, ********.00, 0.00, ********.00, NULL, 'COD', 'tunai', NULL, 'Jl. Raya Industri No. 123, Jakarta Timur', 'Pembelian konsinyasi TV dan speaker', '2025-06-06 09:57:01', '2025-06-06 09:57:01', 'admin', NULL, NULL, NULL);

-- Dumping structure for table toko_elektronik.pembelian_detail
CREATE TABLE IF NOT EXISTS `pembelian_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_pembelian` int(11) NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty` decimal(10,2) NOT NULL,
  `harga_satuan` decimal(15,2) NOT NULL,
  `diskon_persen` decimal(5,2) DEFAULT 0.00,
  `diskon_nominal` decimal(15,2) DEFAULT 0.00,
  `subtotal_sebelum_diskon` decimal(15,2) NOT NULL,
  `subtotal_setelah_diskon` decimal(15,2) NOT NULL,
  `ppn_persen` decimal(5,2) DEFAULT 11.00,
  `ppn_nominal` decimal(15,2) DEFAULT 0.00,
  `total_akhir` decimal(15,2) NOT NULL,
  `keterangan` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_pembelian` (`id_pembelian`),
  KEY `id_barang` (`id_barang`),
  KEY `id_gudang` (`id_gudang`),
  CONSTRAINT `pembelian_detail_ibfk_1` FOREIGN KEY (`id_pembelian`) REFERENCES `pembelian` (`id`) ON DELETE CASCADE,
  CONSTRAINT `pembelian_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`),
  CONSTRAINT `pembelian_detail_ibfk_3` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.pembelian_detail: ~5 rows (approximately)
INSERT INTO `pembelian_detail` (`id`, `id_pembelian`, `id_barang`, `id_gudang`, `qty`, `harga_satuan`, `diskon_persen`, `diskon_nominal`, `subtotal_sebelum_diskon`, `subtotal_setelah_diskon`, `ppn_persen`, `ppn_nominal`, `total_akhir`, `keterangan`) VALUES
	(1, 1, 1, 4, 10.00, 4500000.00, 0.00, 0.00, 45000000.00, 45000000.00, 11.00, 4950000.00, 49950000.00, 'Samsung Galaxy A54 untuk stok'),
	(2, 1, 2, 4, 5.00, 7800000.00, 0.00, 0.00, 39000000.00, 39000000.00, 11.00, 4290000.00, 43290000.00, 'Laptop ASUS untuk stok'),
	(3, 2, 3, 4, 3.00, 4200000.00, 0.00, 0.00, 12600000.00, 12600000.00, 11.00, 1386000.00, 13986000.00, 'Smart TV LG untuk meeting room'),
	(4, 2, 4, 4, 5.00, 3200000.00, 0.00, 0.00, 16000000.00, 16000000.00, 11.00, 1760000.00, 17760000.00, 'Headphone Sony untuk audio'),
	(5, 3, 8, 5, 10.00, 1800000.00, 0.00, 0.00, 18000000.00, 18000000.00, 11.00, 1980000.00, ********.00, 'Speaker JBL konsinyasi');

-- Dumping structure for table toko_elektronik.pembelian_pembayaran
CREATE TABLE IF NOT EXISTS `pembelian_pembayaran` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_pembelian` int(11) NOT NULL,
  `nomor_pembayaran` varchar(50) NOT NULL,
  `tanggal_pembayaran` date NOT NULL,
  `jumlah_bayar` decimal(15,2) NOT NULL,
  `metode_pembayaran` enum('tunai','transfer','kredit','cek','giro') NOT NULL,
  `nomor_referensi` varchar(100) DEFAULT NULL,
  `bank_pengirim` varchar(100) DEFAULT NULL,
  `bank_penerima` varchar(100) DEFAULT NULL,
  `keterangan` text DEFAULT NULL,
  `bukti_pembayaran` varchar(255) DEFAULT NULL,
  `status` enum('pending','verified','rejected') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` varchar(50) DEFAULT NULL,
  `verified_by` varchar(50) DEFAULT NULL,
  `verified_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_pembayaran` (`nomor_pembayaran`),
  KEY `id_pembelian` (`id_pembelian`),
  KEY `idx_pembayaran_tanggal` (`tanggal_pembayaran`),
  KEY `idx_pembayaran_status` (`status`),
  CONSTRAINT `pembelian_pembayaran_ibfk_1` FOREIGN KEY (`id_pembelian`) REFERENCES `pembelian` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.pembelian_pembayaran: ~2 rows (approximately)
INSERT INTO `pembelian_pembayaran` (`id`, `id_pembelian`, `nomor_pembayaran`, `tanggal_pembayaran`, `jumlah_bayar`, `metode_pembayaran`, `nomor_referensi`, `bank_pengirim`, `bank_penerima`, `keterangan`, `bukti_pembayaran`, `status`, `created_at`, `updated_at`, `created_by`, `verified_by`, `verified_at`) VALUES
	(1, 2, 'PAY-PB-********-001', '2025-01-15', ********.00, 'transfer', 'TRF123456789', 'BCA', 'Mandiri', 'Pembayaran sebagian pembelian PB-********-0002', NULL, 'verified', '2025-06-06 09:57:01', '2025-06-06 09:57:01', 'admin', NULL, NULL),
	(2, 2, 'PAY-PB-********-001', '2025-06-07', ********.00, 'transfer', '1111', 'BCA', 'BCA', 'Pembayaran lanjutan untuk pembelian PB-********-0002', NULL, 'verified', '2025-06-07 12:10:17', '2025-06-07 12:10:17', NULL, NULL, '2025-06-07 19:10:17');

-- Dumping structure for table toko_elektronik.pembelian_tracking
CREATE TABLE IF NOT EXISTS `pembelian_tracking` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_pembelian` int(11) NOT NULL,
  `status` enum('draft','disetujui','dipesan','diterima','selesai','dibatalkan') NOT NULL,
  `tanggal_status` datetime NOT NULL,
  `keterangan` text DEFAULT NULL,
  `created_by` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `id_pembelian` (`id_pembelian`),
  KEY `idx_tracking_tanggal` (`tanggal_status`),
  CONSTRAINT `pembelian_tracking_ibfk_1` FOREIGN KEY (`id_pembelian`) REFERENCES `pembelian` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.pembelian_tracking: ~8 rows (approximately)
INSERT INTO `pembelian_tracking` (`id`, `id_pembelian`, `status`, `tanggal_status`, `keterangan`, `created_by`, `created_at`) VALUES
	(1, 1, 'draft', '2025-01-01 08:00:00', 'Pembelian dibuat', 'admin', '2025-06-06 09:57:01'),
	(2, 2, 'draft', '2025-01-02 08:00:00', 'Pembelian dibuat', 'admin', '2025-06-06 09:57:01'),
	(3, 2, 'disetujui', '2025-01-02 10:00:00', 'Pembelian disetujui manager', 'admin', '2025-06-06 09:57:01'),
	(4, 3, 'draft', '2025-01-03 08:00:00', 'Pembelian dibuat', 'admin', '2025-06-06 09:57:01'),
	(5, 3, 'disetujui', '2025-01-03 09:00:00', 'Pembelian disetujui manager', 'admin', '2025-06-06 09:57:01'),
	(6, 3, 'dipesan', '2025-01-03 11:00:00', 'PO dikirim ke supplier', 'admin', '2025-06-06 09:57:01'),
	(7, 2, 'dipesan', '2025-06-07 19:09:29', 'Status berubah dari disetujui menjadi dipesan', NULL, '2025-06-07 12:09:29'),
	(8, 2, 'diterima', '2025-06-07 19:10:37', 'Status berubah dari dipesan menjadi diterima', NULL, '2025-06-07 12:10:37');

-- Dumping structure for table toko_elektronik.penyesuaian_stok
CREATE TABLE IF NOT EXISTS `penyesuaian_stok` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_penyesuaian` varchar(50) NOT NULL,
  `tanggal` date NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty_awal` decimal(10,2) NOT NULL,
  `qty_baru` decimal(10,2) NOT NULL,
  `qty_selisih` decimal(10,2) NOT NULL,
  `jenis_penyesuaian` enum('PENAMBAHAN','PENGURANGAN') NOT NULL,
  `alasan` text DEFAULT NULL,
  `keterangan` text DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `aktif` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_penyesuaian` (`kode_penyesuaian`),
  KEY `idx_penyesuaian_stok_tanggal` (`tanggal`),
  KEY `idx_penyesuaian_stok_barang` (`id_barang`),
  KEY `idx_penyesuaian_stok_gudang` (`id_gudang`),
  KEY `idx_penyesuaian_stok_kode` (`kode_penyesuaian`),
  CONSTRAINT `penyesuaian_stok_ibfk_1` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `penyesuaian_stok_ibfk_2` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.penyesuaian_stok: ~2 rows (approximately)
INSERT INTO `penyesuaian_stok` (`id`, `kode_penyesuaian`, `tanggal`, `id_barang`, `id_gudang`, `qty_awal`, `qty_baru`, `qty_selisih`, `jenis_penyesuaian`, `alasan`, `keterangan`, `user_id`, `aktif`, `created_at`, `updated_at`) VALUES
	(1, 'PST0001', '2025-01-15', 1, 4, 100.00, 95.00, -5.00, 'PENGURANGAN', 'Barang rusak ditemukan saat stock opname', 'Penyesuaian stok bulanan', NULL, 1, '2025-06-01 03:19:47', '2025-06-01 03:19:47'),
	(2, 'PST0002', '2025-01-15', 2, 4, 50.00, 55.00, 5.00, 'PENAMBAHAN', 'Barang ditemukan di area yang tidak tercatat', 'Koreksi stok setelah audit', NULL, 1, '2025-06-01 03:19:47', '2025-06-01 03:19:47');

-- Dumping structure for table toko_elektronik.pesanan
CREATE TABLE IF NOT EXISTS `pesanan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nomor_pesanan` varchar(50) NOT NULL,
  `tanggal_pesanan` date NOT NULL,
  `id_pelanggan` int(11) NOT NULL,
  `jenis_pesanan` enum('manual','android') NOT NULL,
  `status` enum('draft','diproses','dikirim','selesai','dibatalkan') NOT NULL DEFAULT 'draft',
  `total_item` int(11) NOT NULL DEFAULT 0,
  `total_qty` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_harga` decimal(15,2) NOT NULL DEFAULT 0.00,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` varchar(50) DEFAULT NULL,
  `updated_by` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_pesanan` (`nomor_pesanan`),
  KEY `id_pelanggan` (`id_pelanggan`),
  CONSTRAINT `pesanan_ibfk_1` FOREIGN KEY (`id_pelanggan`) REFERENCES `pelanggan` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.pesanan: ~6 rows (approximately)
INSERT INTO `pesanan` (`id`, `nomor_pesanan`, `tanggal_pesanan`, `id_pelanggan`, `jenis_pesanan`, `status`, `total_item`, `total_qty`, `total_harga`, `keterangan`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
	(1, 'PSN-20250101-0001', '2025-01-01', 1, 'manual', 'selesai', 2, 3.00, 19300000.00, 'Pesanan smartphone dan laptop untuk kantor', '2025-06-04 21:40:53', '2025-06-08 14:39:56', 'admin', NULL),
	(2, 'PSN-********-0002', '2025-01-02', 2, 'android', 'diproses', 3, 5.00, 16800000.00, 'Pesanan peralatan elektronik untuk meeting room', '2025-06-04 21:40:53', '2025-06-04 21:40:53', 'admin', 'admin'),
	(3, 'PSN-20250103-0003', '2025-01-03', 3, 'manual', 'dikirim', 2, 4.00, 14000000.00, 'Pesanan TV dan speaker untuk ruang presentasi', '2025-06-04 21:40:53', '2025-06-04 21:40:53', 'admin', 'admin'),
	(4, 'PSN-20250104-0004', '2025-01-04', 4, 'android', 'selesai', 3, 7.00, 9100000.00, 'Pesanan aksesoris komputer dan gadget', '2025-06-04 21:40:53', '2025-06-04 21:40:53', 'admin', 'admin'),
	(5, 'PSN-20250105-0005', '2025-01-05', 5, 'manual', 'dikirim', 3, 5.00, 40400000.00, 'Pesanan kamera dan tablet untuk dokumentasi', '2025-06-04 21:40:53', '2025-06-08 05:31:00', 'admin', NULL),
	(10, 'PSN202506080001', '2025-06-08', 6, 'manual', 'dikirim', 1, 1.00, 5200000.00, '', '2025-06-08 07:18:06', '2025-06-08 07:18:48', NULL, NULL);

-- Dumping structure for table toko_elektronik.pesanan_detail
CREATE TABLE IF NOT EXISTS `pesanan_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_pesanan` int(11) NOT NULL,
  `id_barang` int(11) NOT NULL,
  `qty` decimal(10,2) NOT NULL,
  `harga_satuan` decimal(15,2) NOT NULL,
  `subtotal` decimal(15,2) NOT NULL,
  `keterangan` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id_pesanan` (`id_pesanan`),
  KEY `id_barang` (`id_barang`),
  CONSTRAINT `pesanan_detail_ibfk_1` FOREIGN KEY (`id_pesanan`) REFERENCES `pesanan` (`id`) ON DELETE CASCADE,
  CONSTRAINT `pesanan_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.pesanan_detail: ~13 rows (approximately)
INSERT INTO `pesanan_detail` (`id`, `id_pesanan`, `id_barang`, `qty`, `harga_satuan`, `subtotal`, `keterangan`) VALUES
	(1, 1, 1, 2.00, 5200000.00, 10400000.00, 'Samsung Galaxy A54 untuk staff'),
	(2, 1, 2, 1.00, 8900000.00, 8900000.00, 'Laptop ASUS untuk manager'),
	(3, 2, 3, 1.00, 4800000.00, 4800000.00, 'Smart TV LG untuk ruang meeting'),
	(4, 2, 4, 2.00, 3800000.00, 7600000.00, 'Headphone Sony untuk audio'),
	(5, 2, 8, 2.00, 2200000.00, 4400000.00, 'Speaker JBL untuk presentasi'),
	(6, 3, 3, 2.00, 4800000.00, 9600000.00, 'Smart TV untuk cabang'),
	(7, 3, 8, 2.00, 2200000.00, 4400000.00, 'Speaker untuk audio system'),
	(8, 4, 4, 1.00, 3800000.00, 3800000.00, 'Headphone premium'),
	(9, 4, 9, 2.00, 1750000.00, 3500000.00, 'Gaming mouse untuk developer'),
	(10, 4, 10, 4.00, 450000.00, 1800000.00, 'Power bank untuk mobile team'),
	(11, 5, 5, 1.00, 9800000.00, 9800000.00, 'Kamera Canon untuk dokumentasi'),
	(12, 5, 6, 2.00, 8500000.00, ********.00, 'iPad Air untuk presentasi'),
	(13, 5, 7, 2.00, 6800000.00, ********.00, 'Apple Watch untuk executive'),
	(14, 10, 1, 1.00, 5200000.00, 5200000.00, '');

-- Dumping structure for table toko_elektronik.satuan
CREATE TABLE IF NOT EXISTS `satuan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_satuan` varchar(20) NOT NULL,
  `nama_satuan` varchar(100) NOT NULL,
  `keterangan` text DEFAULT NULL,
  `aktif` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_satuan` (`kode_satuan`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.satuan: ~20 rows (approximately)
INSERT INTO `satuan` (`id`, `kode_satuan`, `nama_satuan`, `keterangan`, `aktif`, `created_at`, `updated_at`) VALUES
	(1, 'PCS', 'Pieces', 'Satuan untuk barang per buah/unit', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(2, 'SET', 'Set', 'Satuan untuk barang dalam bentuk set/paket', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(3, 'UNIT', 'Unit', 'Satuan untuk barang per unit', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(4, 'BOX', 'Box', 'Satuan untuk barang dalam kemasan box', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(5, 'PACK', 'Pack', 'Satuan untuk barang dalam kemasan pack', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(6, 'KG', 'Kilogram', 'Satuan berat dalam kilogram', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(7, 'GRAM', 'Gram', 'Satuan berat dalam gram', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(8, 'METER', 'Meter', 'Satuan panjang dalam meter', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(9, 'CM', 'Centimeter', 'Satuan panjang dalam centimeter', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(10, 'LITER', 'Liter', 'Satuan volume dalam liter', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(11, 'ML', 'Mililiter', 'Satuan volume dalam mililiter', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(12, 'DOZEN', 'Dozen', 'Satuan untuk 12 buah', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(13, 'GROSS', 'Gross', 'Satuan untuk 144 buah (12 dozen)', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(14, 'ROLL', 'Roll', 'Satuan untuk barang berbentuk gulungan', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(15, 'SHEET', 'Sheet', 'Satuan untuk barang berbentuk lembaran', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(16, 'BOTTLE', 'Bottle', 'Satuan untuk barang dalam botol', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(17, 'CAN', 'Can', 'Satuan untuk barang dalam kaleng', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(18, 'TUBE', 'Tube', 'Satuan untuk barang dalam tube', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(19, 'PAIR', 'Pair', 'Satuan untuk barang berpasangan', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47'),
	(20, 'STRIP', 'Strip', 'Satuan untuk obat dalam strip', 1, '2025-05-29 08:37:47', '2025-05-29 08:37:47');

-- Dumping structure for procedure toko_elektronik.sp_update_pembelian_totals
DELIMITER //
CREATE PROCEDURE `sp_update_pembelian_totals`(IN p_id_pembelian INT)
BEGIN
    DECLARE v_total_item INT DEFAULT 0;
    DECLARE v_total_qty DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_subtotal DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_total_setelah_diskon DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_total_ppn DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_total_akhir DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_diskon_nominal DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_biaya_pengiriman DECIMAL(15,2) DEFAULT 0.00;
    
    -- Hitung total dari detail
    SELECT 
        COUNT(*),
        COALESCE(SUM(qty), 0),
        COALESCE(SUM(subtotal_sebelum_diskon), 0),
        COALESCE(SUM(subtotal_setelah_diskon), 0),
        COALESCE(SUM(ppn_nominal), 0),
        COALESCE(SUM(total_akhir), 0)
    INTO 
        v_total_item,
        v_total_qty,
        v_subtotal,
        v_total_setelah_diskon,
        v_total_ppn,
        v_total_akhir
    FROM pembelian_detail 
    WHERE id_pembelian = p_id_pembelian;
    
    -- Ambil diskon dan biaya pengiriman dari header
    SELECT 
        COALESCE(diskon_nominal, 0),
        COALESCE(biaya_pengiriman, 0)
    INTO 
        v_diskon_nominal,
        v_biaya_pengiriman
    FROM pembelian 
    WHERE id = p_id_pembelian;
    
    -- Update header pembelian
    UPDATE pembelian SET
        total_item = v_total_item,
        total_qty = v_total_qty,
        subtotal = v_subtotal,
        total_sebelum_pajak = v_total_setelah_diskon,
        ppn_nominal = v_total_ppn,
        total_setelah_pajak = v_total_setelah_diskon + v_total_ppn,
        total_akhir = v_total_setelah_diskon + v_total_ppn + v_biaya_pengiriman,
        updated_at = NOW()
    WHERE id = p_id_pembelian;
END//
DELIMITER ;

-- Dumping structure for table toko_elektronik.stok_barang
CREATE TABLE IF NOT EXISTS `stok_barang` (
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `qty_terakhir` decimal(12,2) NOT NULL DEFAULT 0.00,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id_barang`,`id_gudang`),
  KEY `id_gudang` (`id_gudang`),
  CONSTRAINT `stok_barang_ibfk_1` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `stok_barang_ibfk_2` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.stok_barang: ~6 rows (approximately)
INSERT INTO `stok_barang` (`id_barang`, `id_gudang`, `qty_terakhir`, `updated_at`) VALUES
	(1, 3, 74.00, '2025-06-07 22:13:27'),
	(1, 4, 99.00, '2025-06-01 04:41:25'),
	(2, 4, 50.00, '2025-06-03 16:00:41'),
	(3, 4, 5.00, '2025-06-01 04:41:25'),
	(9, 5, 0.00, '2025-06-07 15:04:56'),
	(9, 6, 1.00, '2025-06-07 14:45:22');

-- Dumping structure for table toko_elektronik.stok_movement
CREATE TABLE IF NOT EXISTS `stok_movement` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tanggal` datetime NOT NULL,
  `id_barang` int(11) NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `tipe_transaksi` enum('pembelian','penjualan','retur_beli','retur_jual','penyesuaian','opname','transfer_masuk','transfer_keluar') NOT NULL,
  `qty_in` decimal(12,2) DEFAULT 0.00,
  `qty_out` decimal(12,2) DEFAULT 0.00,
  `keterangan` varchar(255) DEFAULT NULL,
  `ref_transaksi` varchar(100) DEFAULT NULL,
  `user_input` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_stok_movement_tanggal` (`tanggal`),
  KEY `idx_stok_movement_barang` (`id_barang`),
  KEY `idx_stok_movement_gudang` (`id_gudang`),
  KEY `idx_stok_movement_tipe` (`tipe_transaksi`),
  KEY `idx_stok_movement_ref` (`ref_transaksi`),
  CONSTRAINT `stok_movement_ibfk_1` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `stok_movement_ibfk_2` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.stok_movement: ~13 rows (approximately)
INSERT INTO `stok_movement` (`id`, `tanggal`, `id_barang`, `id_gudang`, `tipe_transaksi`, `qty_in`, `qty_out`, `keterangan`, `ref_transaksi`, `user_input`, `created_at`) VALUES
	(1, '2025-06-01 10:51:08', 1, 4, 'opname', 100.00, 0.00, 'Stok awal sistem', 'INIT001', 'system', '2025-06-01 03:51:08'),
	(2, '2025-06-01 10:51:08', 2, 4, 'opname', 50.00, 0.00, 'Stok awal sistem', 'INIT002', 'system', '2025-06-01 03:51:08'),
	(3, '2025-06-01 10:51:08', 1, 3, 'opname', 75.00, 0.00, 'Stok awal sistem', 'INIT003', 'system', '2025-06-01 03:51:08'),
	(4, '2025-01-15 11:41:25', 2, 4, 'opname', 1.00, 0.00, 'Stock Opname: terselip', 'SO-2025-001', 'Admin', '2025-06-01 04:41:25'),
	(5, '2025-01-15 11:41:25', 1, 4, 'opname', 0.00, 1.00, 'Stock Opname: hilang', 'SO-2025-001', 'Admin', '2025-06-01 04:41:25'),
	(6, '2025-01-15 11:41:25', 3, 4, 'opname', 5.00, 0.00, 'Stock Opname: Tambahan', 'SO-2025-001', 'Admin', '2025-06-01 04:41:25'),
	(7, '2025-06-01 15:12:47', 2, 4, 'penyesuaian', 0.00, 1.00, 'Barang Keluar: ', 'BK-2025-06-001', 'user_1', '2025-06-01 08:12:47'),
	(9, '2025-06-02 09:08:17', 2, 4, 'penyesuaian', 1.00, 0.00, 'Pembatalan Transfer - TR-2025-06-001', 'CANCEL-TR-2025-06-001', 'system', '2025-06-02 02:08:17'),
	(10, '2025-06-02 17:43:54', 2, 4, 'penyesuaian', 1.00, 0.00, 'Pembatalan Transfer - TR-2025-06-002', 'CANCEL-TR-2025-06-002', 'system', '2025-06-02 10:43:54'),
	(11, '2025-06-03 23:00:41', 2, 4, 'opname', 0.00, 2.00, 'Stock Opname: ', 'SO-2025-003', 'Admin', '2025-06-03 16:00:41'),
	(12, '2025-01-15 21:45:22', 9, 5, 'pembelian', 1.00, 0.00, 'Barang Masuk - pembelian: 1', 'BM-2025-001', 'user_1', '2025-06-07 14:45:22'),
	(13, '2025-01-15 21:45:22', 9, 6, 'pembelian', 1.00, 0.00, 'Barang Masuk - pembelian: 1', 'BM-2025-001', 'user_1', '2025-06-07 14:45:22'),
	(15, '2025-06-07 22:04:56', 9, 5, 'penjualan', 0.00, 1.00, 'Barang Keluar: ', 'BK-2025-06-002', 'user_1', '2025-06-07 15:04:56'),
	(16, '2025-06-07 05:13:27', 1, 3, 'opname', 0.00, 1.00, 'Stock Opname: ', 'SO-2025-005', 'Admin', '2025-06-07 22:13:27');

-- Dumping structure for table toko_elektronik.stok_opname
CREATE TABLE IF NOT EXISTS `stok_opname` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `nomor_opname` varchar(50) NOT NULL,
  `tanggal_opname` date NOT NULL,
  `id_gudang` int(11) NOT NULL,
  `keterangan` varchar(255) DEFAULT NULL,
  `status` enum('draft','final') DEFAULT 'draft',
  `total_item` int(11) DEFAULT 0,
  `total_selisih_positif` decimal(15,2) DEFAULT 0.00,
  `total_selisih_negatif` decimal(15,2) DEFAULT 0.00,
  `user_input` varchar(100) DEFAULT NULL,
  `user_final` varchar(100) DEFAULT NULL,
  `tanggal_final` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_opname` (`nomor_opname`),
  KEY `idx_stok_opname_tanggal` (`tanggal_opname`),
  KEY `idx_stok_opname_gudang` (`id_gudang`),
  KEY `idx_stok_opname_status` (`status`),
  KEY `idx_stok_opname_nomor` (`nomor_opname`),
  CONSTRAINT `stok_opname_ibfk_1` FOREIGN KEY (`id_gudang`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.stok_opname: ~5 rows (approximately)
INSERT INTO `stok_opname` (`id`, `nomor_opname`, `tanggal_opname`, `id_gudang`, `keterangan`, `status`, `total_item`, `total_selisih_positif`, `total_selisih_negatif`, `user_input`, `user_final`, `tanggal_final`, `created_at`, `updated_at`) VALUES
	(1, 'SO-2025-001', '2025-01-15', 4, 'Stock Opname Bulanan Januari 2025', 'final', 3, 6.00, 1.00, 'admin', 'Admin', '2025-06-01 11:41:25', '2025-06-01 04:10:10', '2025-06-01 04:41:25'),
	(2, 'SO-2025-002', '2025-06-03', 5, 'test', 'draft', 1, 1.00, 0.00, 'Admin', NULL, NULL, '2025-06-03 14:52:12', '2025-06-07 22:30:35'),
	(3, 'SO-2025-003', '2025-06-03', 4, '', 'final', 1, 0.00, 2.00, 'Admin', 'Admin', '2025-06-03 23:00:41', '2025-06-03 14:52:35', '2025-06-03 16:00:41'),
	(4, 'SO-2025-004', '2025-06-03', 4, '', 'final', 3, 0.00, 0.00, 'Admin', 'Admin', '2025-06-03 23:02:29', '2025-06-03 16:02:03', '2025-06-03 16:02:29'),
	(5, 'SO-2025-005', '2025-06-07', 3, '', 'final', 1, 0.00, 1.00, 'Admin', 'Admin', '2025-06-08 05:13:27', '2025-06-07 22:12:33', '2025-06-07 22:13:27');

-- Dumping structure for table toko_elektronik.stok_opname_detail
CREATE TABLE IF NOT EXISTS `stok_opname_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `id_opname` bigint(20) unsigned NOT NULL,
  `id_barang` int(11) NOT NULL,
  `qty_sistem` decimal(12,2) NOT NULL DEFAULT 0.00,
  `qty_fisik` decimal(12,2) NOT NULL DEFAULT 0.00,
  `selisih` decimal(12,2) GENERATED ALWAYS AS (`qty_fisik` - `qty_sistem`) STORED,
  `keterangan` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_opname_barang` (`id_opname`,`id_barang`),
  KEY `idx_stok_opname_detail_opname` (`id_opname`),
  KEY `idx_stok_opname_detail_barang` (`id_barang`),
  KEY `idx_stok_opname_detail_selisih` (`selisih`),
  CONSTRAINT `stok_opname_detail_ibfk_1` FOREIGN KEY (`id_opname`) REFERENCES `stok_opname` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `stok_opname_detail_ibfk_2` FOREIGN KEY (`id_barang`) REFERENCES `barang` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.stok_opname_detail: ~9 rows (approximately)
INSERT INTO `stok_opname_detail` (`id`, `id_opname`, `id_barang`, `qty_sistem`, `qty_fisik`, `keterangan`, `created_at`, `updated_at`) VALUES
	(1, 1, 2, 50.00, 51.00, 'terselip', '2025-06-01 04:38:58', '2025-06-01 04:39:46'),
	(2, 1, 1, 100.00, 99.00, 'hilang', '2025-06-01 04:38:58', '2025-06-01 04:40:19'),
	(3, 1, 3, 0.00, 5.00, 'Tambahan', '2025-06-01 04:41:12', '2025-06-01 04:41:12'),
	(12, 3, 2, 52.00, 50.00, '', '2025-06-03 15:59:58', '2025-06-03 16:00:19'),
	(15, 4, 2, 50.00, 50.00, '', '2025-06-03 16:02:12', '2025-06-03 16:02:12'),
	(16, 4, 3, 5.00, 5.00, '', '2025-06-03 16:02:12', '2025-06-03 16:02:12'),
	(17, 4, 1, 99.00, 99.00, '', '2025-06-03 16:02:12', '2025-06-03 16:02:12'),
	(20, 5, 1, 75.00, 74.00, '', '2025-06-07 22:12:55', '2025-06-07 22:13:10'),
	(22, 2, 9, 0.00, 1.00, '', '2025-06-07 22:30:24', '2025-06-07 22:30:35');

-- Dumping structure for table toko_elektronik.supplier
CREATE TABLE IF NOT EXISTS `supplier` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode` varchar(50) NOT NULL,
  `nama` varchar(255) NOT NULL,
  `no_telepon` varchar(255) NOT NULL,
  `alamat` text DEFAULT NULL,
  `alamat_kirim` text DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `nama_pic` varchar(100) DEFAULT NULL,
  `telepon_pic` varchar(20) DEFAULT NULL,
  `npwp` varchar(30) DEFAULT NULL,
  `no_ktp` varchar(30) DEFAULT NULL,
  `is_pkp` tinyint(1) DEFAULT 0,
  `status_aktif` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode` (`kode`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.supplier: ~12 rows (approximately)
INSERT INTO `supplier` (`id`, `kode`, `nama`, `no_telepon`, `alamat`, `alamat_kirim`, `email`, `nama_pic`, `telepon_pic`, `npwp`, `no_ktp`, `is_pkp`, `status_aktif`, `created_at`, `updated_at`) VALUES
	(1, 'SUP0001', 'PT Elektronik Supplier Utama', '081234567890', 'Jl. Gajah Mada No. 45, Petojo Utara, Jakarta Pusat 10130', 'Jl. Gajah Mada No. 45, Petojo Utara, Jakarta Pusat 10130', '<EMAIL>', 'Ahmad Fauzi', '081234567891', '01.234.567.8-901.000', '3171012345678901', 1, 1, '2025-05-29 13:55:52', '2025-05-31 03:21:22'),
	(2, 'SUP0002', 'PT Elektronik Distributor Indonesia', '081234567890', 'Jl. Mangga Dua Raya No. 88, Sawah Besar, Jakarta Pusat 10730', 'Jl. Mangga Dua Raya No. 88, Sawah Besar, Jakarta Pusat 10730', '<EMAIL>', 'Bambang Sutrisno', '081234567891', '01.234.567.8-901.000', '3171012345678901', 1, 1, '2025-05-29 13:56:50', '2025-05-31 03:21:22'),
	(3, 'SUP0003', 'PT Sumber Elektronik Nusantara', '081345678901', 'Jl. Glodok Plaza Blok A No. 15, Taman Sari, Jakarta Barat 11120', 'Jl. Glodok Plaza Blok A No. 15, Taman Sari, Jakarta Barat 11120', '<EMAIL>', 'Siti Nurhaliza', '081345678902', '01.345.678.9-012.000', '3174023456789012', 1, 1, '2025-05-29 13:56:50', '2025-05-31 03:21:22'),
	(4, 'SUP0004', 'PT Mega Elektronik Supply', '081456789012', 'Jl. Hayam Wuruk No. 127, Kemayoran, Jakarta Pusat 10640', 'Jl. Hayam Wuruk No. 127, Kemayoran, Jakarta Pusat 10640', '<EMAIL>', 'Agus Setiawan', '081456789013', '01.456.789.0-123.000', '3171034567890123', 1, 1, '2025-05-29 13:56:50', '2025-05-31 03:21:22'),
	(5, 'SUP0005', 'PT Prima Elektronik Tangerang', '081567890123', 'Jl. MH Thamrin No. 45, Cikokol, Tangerang Kota, Banten 15117', 'Jl. MH Thamrin No. 45, Cikokol, Tangerang Kota, Banten 15117', '<EMAIL>', 'Rina Wulandari', '081567890124', '01.567.890.1-234.000', '3671045678901234', 1, 1, '2025-05-29 13:56:50', '2025-05-31 03:21:22'),
	(6, 'SUP0006', 'PT Global Elektronik Serpong', '081678901234', 'Jl. Raya Serpong KM 7 No. 22, Serpong, Tangerang Selatan 15310', 'Jl. Raya Serpong KM 7 No. 22, Serpong, Tangerang Selatan 15310', '<EMAIL>', 'Dedi Kurniawan', '081678901235', '01.678.901.2-345.000', '3674056789012345', 1, 1, '2025-05-29 13:56:50', '2025-05-31 03:21:22'),
	(7, 'SUP0007', 'PT Teknologi Elektronik Jakarta', '081789012345', 'Jl. Kramat Raya No. 98, Senen, Jakarta Pusat 10420', 'Jl. Kramat Raya No. 98, Senen, Jakarta Pusat 10420', '<EMAIL>', 'Indra Gunawan', '081789012346', '01.789.012.3-456.000', '3171067890123456', 1, 1, '2025-05-29 13:56:50', '2025-05-31 03:21:22'),
	(8, 'SUP0008', 'PT Elektronik Wholesale Center', '081890123456', 'Jl. Pademangan Timur No. 55, Pademangan, Jakarta Utara 14410', 'Jl. Pademangan Timur No. 55, Pademangan, Jakarta Utara 14410', '<EMAIL>', 'Maya Kartika', '081890123457', '01.890.123.4-567.000', '3175078901234567', 1, 1, '2025-05-29 13:56:50', '2025-05-31 03:21:22'),
	(9, 'SUP0009', 'PT Elektronik Parts Supplier', '081901234567', 'Jl. Kebon Jeruk Raya No. 27, Kebon Jeruk, Jakarta Barat 11530', 'Jl. Kebon Jeruk Raya No. 27, Kebon Jeruk, Jakarta Barat 11530', '<EMAIL>', 'Hendra Wijaya', '081901234568', '01.901.234.5-678.000', '3174089012345678', 1, 1, '2025-05-29 13:56:50', '2025-05-31 03:21:22'),
	(10, 'SUP0010', 'PT Smart Electronics Bintaro', '082012345678', 'Jl. Bintaro Raya No. 88, Pesanggrahan, Jakarta Selatan 12330', 'Jl. Bintaro Raya No. 88, Pesanggrahan, Jakarta Selatan 12330', '<EMAIL>', 'Fitri Handayani', '082012345679', '02.012.345.6-789.000', '3175090123456789', 1, 1, '2025-05-29 13:56:50', '2025-05-31 03:21:22'),
	(11, 'SUP0011', 'PT Elektronik Trading House', '082123456789', 'Jl. Sunter Permai Raya No. 15, Tanjung Priok, Jakarta Utara 14350', 'Jl. Sunter Permai Raya No. 15, Tanjung Priok, Jakarta Utara 14350', '<EMAIL>', 'Budi Santoso', '082123456790', '02.123.456.7-890.000', '3175101234567890', 1, 1, '2025-05-29 13:56:50', '2025-05-31 03:21:22');

-- Dumping structure for table toko_elektronik.tbl_akses_menu
CREATE TABLE IF NOT EXISTS `tbl_akses_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `id_level` int(11) NOT NULL,
  `id_menu` int(11) NOT NULL,
  `view` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N',
  `add` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N',
  `edit` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N',
  `delete` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N',
  `print` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N',
  `upload` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N',
  `download` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N',
  PRIMARY KEY (`id`),
  KEY `id_menu` (`id_menu`),
  KEY `id_level` (`id_level`),
  CONSTRAINT `tbl_akses_menu_ibfk_1` FOREIGN KEY (`id_level`) REFERENCES `tbl_userlevel` (`id_level`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `tbl_akses_menu_ibfk_2` FOREIGN KEY (`id_menu`) REFERENCES `tbl_menu` (`id_menu`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=444 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- Dumping data for table toko_elektronik.tbl_akses_menu: ~19 rows (approximately)
INSERT INTO `tbl_akses_menu` (`id`, `id_level`, `id_menu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES
	(1, 1, 1, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(69, 1, 2, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(94, 1, 3, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(207, 1, 4, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(427, 1, 6, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(428, 1, 7, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(429, 1, 8, 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N'),
	(430, 1, 9, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(431, 1, 10, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(434, 2, 1, 'Y', 'N', 'N', 'N', 'N', 'N', 'N'),
	(435, 2, 2, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(436, 2, 3, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(437, 2, 4, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(438, 2, 6, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(439, 2, 7, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(440, 2, 8, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(441, 2, 9, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(442, 2, 10, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(443, 2, 13, 'N', 'N', 'N', 'N', 'N', 'N', 'N');

-- Dumping structure for table toko_elektronik.tbl_akses_submenu
CREATE TABLE IF NOT EXISTS `tbl_akses_submenu` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `id_level` int(11) NOT NULL,
  `id_submenu` int(11) NOT NULL,
  `view` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N',
  `add` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N',
  `edit` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N',
  `delete` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N',
  `print` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N',
  `upload` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N',
  `download` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'N',
  PRIMARY KEY (`id`),
  KEY `id_level` (`id_level`),
  KEY `id_submenu` (`id_submenu`),
  CONSTRAINT `tbl_akses_submenu_ibfk_1` FOREIGN KEY (`id_level`) REFERENCES `tbl_userlevel` (`id_level`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `tbl_akses_submenu_ibfk_2` FOREIGN KEY (`id_submenu`) REFERENCES `tbl_submenu` (`id_submenu`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=391 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- Dumping data for table toko_elektronik.tbl_akses_submenu: ~44 rows (approximately)
INSERT INTO `tbl_akses_submenu` (`id`, `id_level`, `id_submenu`, `view`, `add`, `edit`, `delete`, `print`, `upload`, `download`) VALUES
	(2, 1, 2, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(4, 1, 1, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(6, 1, 3, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(9, 1, 4, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(209, 1, 5, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(289, 1, 6, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(295, 1, 7, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(351, 1, 71, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(352, 1, 72, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(353, 1, 73, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(354, 1, 74, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(355, 1, 75, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(356, 1, 76, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(358, 1, 78, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(359, 1, 79, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(360, 1, 80, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(361, 1, 81, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(362, 1, 82, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(363, 1, 83, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N'),
	(364, 2, 1, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(365, 2, 2, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(366, 2, 3, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(367, 2, 4, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(368, 2, 5, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(369, 2, 6, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(370, 2, 7, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(371, 2, 71, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(372, 2, 72, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(373, 2, 73, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(374, 2, 74, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(375, 2, 75, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(376, 2, 76, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(377, 2, 78, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(378, 2, 79, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(379, 2, 80, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(380, 2, 81, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(381, 2, 82, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(382, 2, 83, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(383, 1, 84, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(384, 2, 84, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(385, 1, 85, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(386, 2, 85, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(387, 1, 86, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(388, 2, 86, 'N', 'N', 'N', 'N', 'N', 'N', 'N'),
	(389, 1, 87, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y'),
	(390, 2, 87, 'N', 'N', 'N', 'N', 'N', 'N', 'N');

-- Dumping structure for table toko_elektronik.tbl_menu
CREATE TABLE IF NOT EXISTS `tbl_menu` (
  `id_menu` int(11) NOT NULL AUTO_INCREMENT,
  `nama_menu` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `link` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `icon` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `urutan` bigint(20) DEFAULT NULL,
  `is_active` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'Y',
  `parent` enum('Y') CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'Y',
  PRIMARY KEY (`id_menu`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- Dumping data for table toko_elektronik.tbl_menu: ~10 rows (approximately)
INSERT INTO `tbl_menu` (`id_menu`, `nama_menu`, `link`, `icon`, `urutan`, `is_active`, `parent`) VALUES
	(1, 'Home', 'dashboard', 'fas fa-tachometer-alt', 0, 'Y', 'Y'),
	(2, 'Konfigurasi', '#', 'fas fa-users-cog', 15, 'Y', 'Y'),
	(3, 'Ganti Password', 'ganti_password', 'fas fa-key', 9, 'Y', 'Y'),
	(4, 'Master', '#', 'fas fa-database', 7, 'Y', 'Y'),
	(6, 'Developer', '#', 'fas fa-tools', 10, 'Y', 'Y'),
	(7, 'Purchase', '#', 'fas fa-credit-card', 3, 'Y', 'Y'),
	(8, 'Sales', '#', 'fas fa-store', 1, 'Y', 'Y'),
	(9, 'Warehouse', '#', 'fas fa-warehouse', 2, 'Y', 'Y'),
	(10, 'Akunting', '#', 'fas fa-receipt', 5, 'Y', 'Y'),
	(13, 'Laporan Stok', 'laporan_stok', 'fa-chart-bar', 90, 'Y', 'Y');

-- Dumping structure for table toko_elektronik.tbl_submenu
CREATE TABLE IF NOT EXISTS `tbl_submenu` (
  `id_submenu` int(11) NOT NULL AUTO_INCREMENT,
  `nama_submenu` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `link` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `icon` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `id_menu` int(11) DEFAULT NULL,
  `is_active` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'Y',
  `urutan` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id_submenu`),
  KEY `id_menu` (`id_menu`),
  CONSTRAINT `tbl_submenu_ibfk_1` FOREIGN KEY (`id_menu`) REFERENCES `tbl_menu` (`id_menu`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=88 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- Dumping data for table toko_elektronik.tbl_submenu: ~19 rows (approximately)
INSERT INTO `tbl_submenu` (`id_submenu`, `nama_submenu`, `link`, `icon`, `id_menu`, `is_active`, `urutan`) VALUES
	(1, 'Menu', 'menu', 'far fa-circle', 2, 'Y', NULL),
	(2, 'Sub Menu', 'submenu', 'far fa-circle', 2, 'Y', NULL),
	(3, 'Aplikasi', 'aplikasi', 'far fa-circle', 2, 'Y', NULL),
	(4, 'User Level', 'userlevel', 'far fa-circle', 2, 'Y', NULL),
	(5, 'Data Pengguna', 'user', 'far fa-circle', 2, 'Y', NULL),
	(6, 'Barang', 'barang', 'far fa-circle', 4, 'Y', NULL),
	(7, 'Satuan', 'satuan', 'far fa-circle', 4, 'Y', NULL),
	(71, 'Create Tabel', 'TemplateController', 'far fa-circle', 6, 'Y', 1),
	(72, 'Gudang', 'gudang', 'far fa-circle', 4, 'Y', 4),
	(73, 'Pelanggan', 'pelanggan', 'far fa-circle', 4, 'Y', 5),
	(74, 'Supplier', 'supplier', 'far fa-circle', 4, 'Y', 3),
	(75, 'Barang Masuk', 'barangmasuk', 'far fa-circle', 9, 'Y', 1),
	(76, 'Barang Keluar', 'barangkeluar', 'far fa-circle', 9, 'Y', 2),
	(78, 'Orders', 'orders', 'far fa-circle', 8, 'Y', 1),
	(79, 'Jenis Pajak', 'jenis_pajak', 'far fa-circle', 4, 'Y', 4),
	(80, 'Penyesuaian Stok', 'Penyesuaian', 'far fa-circle', 9, 'Y', 3),
	(81, 'Stok Opname', 'stokopname', 'far fa-circle', 9, 'Y', 4),
	(82, 'Transfer Stok', 'transferstok', 'far fa-circle', 9, 'Y', 5),
	(83, 'Laporan Stok', 'laporan_stok', 'far fa-circle', 9, 'Y', 6),
	(84, 'Pesanan', 'Pesanan', 'far fa-circle', 8, 'Y', 1),
	(85, 'Pengiriman', 'Pengiriman', 'far fa-circle', 8, 'Y', 2),
	(86, 'Faktur Penjualan', 'FakturPenjualan', 'far fa-circle', 8, 'Y', 3),
	(87, 'Pembelian', 'Pembelian', 'far fa-circle', 7, 'Y', 1);

-- Dumping structure for table toko_elektronik.tbl_user
CREATE TABLE IF NOT EXISTS `tbl_user` (
  `id_user` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `full_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `id_level` int(11) DEFAULT NULL,
  `image` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `nohp` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `is_active` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'Y',
  PRIMARY KEY (`id_user`),
  KEY `id_level` (`id_level`),
  CONSTRAINT `tbl_user_ibfk_1` FOREIGN KEY (`id_level`) REFERENCES `tbl_userlevel` (`id_level`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- Dumping data for table toko_elektronik.tbl_user: ~0 rows (approximately)
INSERT INTO `tbl_user` (`id_user`, `username`, `full_name`, `password`, `id_level`, `image`, `nohp`, `email`, `is_active`) VALUES
	(1, 'admin', 'Administrator', '$2y$05$Bl1UXpDrO8843SqKlnGkq.AjnPhDIGAbfKAoVUkqpUAp4um3LtrbW', 1, 'admin.jpg', '08129837323', '<EMAIL>', 'Y');

-- Dumping structure for table toko_elektronik.tbl_userlevel
CREATE TABLE IF NOT EXISTS `tbl_userlevel` (
  `id_level` int(11) NOT NULL AUTO_INCREMENT,
  `nama_level` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id_level`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- Dumping data for table toko_elektronik.tbl_userlevel: ~2 rows (approximately)
INSERT INTO `tbl_userlevel` (`id_level`, `nama_level`) VALUES
	(1, 'Administrator'),
	(2, 'user');

-- Dumping structure for table toko_elektronik.transfer_stok
CREATE TABLE IF NOT EXISTS `transfer_stok` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `nomor_transfer` varchar(50) NOT NULL,
  `tanggal_transfer` date NOT NULL,
  `gudang_asal_id` int(11) NOT NULL,
  `gudang_tujuan_id` int(11) NOT NULL,
  `keterangan` text DEFAULT NULL,
  `status` enum('draft','dikirim','diterima','batal') DEFAULT 'draft',
  `tanggal_kirim` datetime DEFAULT NULL,
  `tanggal_terima` datetime DEFAULT NULL,
  `total_item` int(11) DEFAULT 0,
  `total_qty` decimal(15,2) DEFAULT 0.00,
  `ref_barang_keluar` varchar(50) DEFAULT NULL,
  `ref_barang_masuk` varchar(50) DEFAULT NULL,
  `dibuat_oleh` varchar(100) DEFAULT NULL,
  `dikirim_oleh` varchar(100) DEFAULT NULL,
  `diterima_oleh` varchar(100) DEFAULT NULL,
  `dibuat_pada` datetime DEFAULT current_timestamp(),
  `diubah_pada` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nomor_transfer` (`nomor_transfer`),
  KEY `idx_transfer_stok_tanggal` (`tanggal_transfer`),
  KEY `idx_transfer_stok_status` (`status`),
  KEY `idx_transfer_stok_gudang_asal` (`gudang_asal_id`),
  KEY `idx_transfer_stok_gudang_tujuan` (`gudang_tujuan_id`),
  KEY `idx_transfer_stok_nomor` (`nomor_transfer`),
  CONSTRAINT `fk_transfer_gudang_asal` FOREIGN KEY (`gudang_asal_id`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `fk_transfer_gudang_tujuan` FOREIGN KEY (`gudang_tujuan_id`) REFERENCES `gudang` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.transfer_stok: ~9 rows (approximately)
INSERT INTO `transfer_stok` (`id`, `nomor_transfer`, `tanggal_transfer`, `gudang_asal_id`, `gudang_tujuan_id`, `keterangan`, `status`, `tanggal_kirim`, `tanggal_terima`, `total_item`, `total_qty`, `ref_barang_keluar`, `ref_barang_masuk`, `dibuat_oleh`, `dikirim_oleh`, `diterima_oleh`, `dibuat_pada`, `diubah_pada`) VALUES
	(4, 'TR-2025-06-001', '2025-06-02', 4, 5, 'test', 'batal', '2025-06-02 08:58:47', NULL, 1, 1.00, 'TK-20250602-0004', NULL, 'Administrator', 'Admin', NULL, '2025-06-02 08:46:14', '2025-06-02 09:08:17'),
	(5, 'TR-2025-06-002', '2025-06-02', 4, 5, 'tes', 'batal', '2025-06-02 15:40:32', NULL, 1, 1.00, 'TK-20250602-0005', NULL, 'Administrator', 'Administrator', NULL, '2025-06-02 15:40:10', '2025-06-02 17:43:54'),
	(6, 'TR-2025-06-003', '2025-06-02', 4, 5, 'ttt', 'diterima', '2025-06-02 17:44:49', '2025-06-02 20:48:04', 1, 1.00, 'TK-20250602-0006', 'TM-20250602-0006', 'Administrator', 'Administrator', 'Administrator', '2025-06-02 17:44:15', '2025-06-02 20:48:04'),
	(7, 'TR-2025-06-004', '2025-06-02', 4, 5, 'tt', 'diterima', '2025-06-02 20:51:00', '2025-06-02 20:51:11', 1, 1.00, 'TK-20250602-0007', 'TM-20250602-0007', 'Administrator', 'Administrator', 'Administrator', '2025-06-02 17:45:12', '2025-06-02 20:51:11'),
	(8, 'TR-2025-06-005', '2025-06-02', 5, 6, '1', 'draft', NULL, NULL, 0, 0.00, NULL, NULL, 'Administrator', NULL, NULL, '2025-06-02 20:51:38', '2025-06-02 20:55:41'),
	(9, 'TR-2025-06-006', '2025-06-07', 5, 6, '', 'draft', NULL, NULL, 0, 0.00, NULL, NULL, 'Administrator', NULL, NULL, '2025-06-08 05:37:18', '2025-06-08 05:37:18'),
	(10, 'TR-2025-06-007', '2025-06-07', 4, 5, '', 'draft', NULL, NULL, 0, 0.00, NULL, NULL, 'Administrator', NULL, NULL, '2025-06-08 05:43:07', '2025-06-08 05:43:07'),
	(11, 'TR-2025-06-008', '2025-06-07', 4, 5, '', 'draft', NULL, NULL, 0, 0.00, NULL, NULL, 'Administrator', NULL, NULL, '2025-06-08 05:49:47', '2025-06-08 05:49:47'),
	(12, 'TR-2025-06-009', '2025-06-07', 4, 3, '', 'draft', NULL, NULL, 0, 0.00, NULL, NULL, 'Administrator', NULL, NULL, '2025-06-08 05:50:22', '2025-06-08 06:05:49');

-- Dumping structure for table toko_elektronik.transfer_stok_detail
CREATE TABLE IF NOT EXISTS `transfer_stok_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `transfer_stok_id` bigint(20) unsigned NOT NULL,
  `barang_id` int(11) NOT NULL,
  `satuan_id` int(11) NOT NULL,
  `qty` decimal(18,2) NOT NULL,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fk_transfer_detail_header` (`transfer_stok_id`),
  KEY `fk_transfer_detail_satuan` (`satuan_id`),
  KEY `idx_transfer_detail_barang` (`barang_id`),
  CONSTRAINT `fk_transfer_detail_barang` FOREIGN KEY (`barang_id`) REFERENCES `barang` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `fk_transfer_detail_header` FOREIGN KEY (`transfer_stok_id`) REFERENCES `transfer_stok` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_transfer_detail_satuan` FOREIGN KEY (`satuan_id`) REFERENCES `satuan` (`id`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Dumping data for table toko_elektronik.transfer_stok_detail: ~4 rows (approximately)
INSERT INTO `transfer_stok_detail` (`id`, `transfer_stok_id`, `barang_id`, `satuan_id`, `qty`, `keterangan`, `created_at`, `updated_at`) VALUES
	(10, 4, 2, 1, 1.00, 'test', '2025-06-02 01:46:27', '2025-06-02 01:46:27'),
	(11, 5, 2, 1, 1.00, 'tes', '2025-06-02 08:40:25', '2025-06-02 08:40:25'),
	(12, 6, 2, 1, 1.00, '1', '2025-06-02 10:44:27', '2025-06-02 10:44:27'),
	(13, 7, 3, 1, 1.00, '1', '2025-06-02 13:50:49', '2025-06-02 13:50:49');

-- Dumping structure for view toko_elektronik.v_barang_keluar_detail
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_barang_keluar_detail` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`id_barang_keluar` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_pengeluaran` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`tanggal` DATE NOT NULL,
	`jenis` ENUM('penjualan','retur_pembelian','transfer_keluar','penyesuaian','produksi','rusak','hilang','sample') NOT NULL COLLATE 'utf8_general_ci',
	`status` ENUM('draft','final') NULL COLLATE 'utf8_general_ci',
	`kode_barang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`nama_barang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_gudang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`qty_keluar` DECIMAL(12,2) NOT NULL,
	`kode_satuan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_satuan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`harga_satuan` DECIMAL(15,2) NULL,
	`total_harga` DECIMAL(15,2) NULL,
	`keterangan` TEXT NULL COLLATE 'utf8_general_ci',
	`created_at` TIMESTAMP NOT NULL
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_barang_keluar_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_barang_keluar_summary` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_pengeluaran` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`tanggal` DATE NOT NULL,
	`jenis` ENUM('penjualan','retur_pembelian','transfer_keluar','penyesuaian','produksi','rusak','hilang','sample') NOT NULL COLLATE 'utf8_general_ci',
	`ref_nomor` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`status` ENUM('draft','final') NULL COLLATE 'utf8_general_ci',
	`total_item` INT(11) NULL,
	`total_qty` DECIMAL(15,2) NULL,
	`kode_pelanggan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_pelanggan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`keterangan` TEXT NULL COLLATE 'utf8_general_ci',
	`created_by` INT(11) NULL,
	`finalized_by` INT(11) NULL,
	`finalized_at` DATETIME NULL,
	`created_at` TIMESTAMP NOT NULL,
	`updated_at` TIMESTAMP NOT NULL
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_barang_keluar_transfer
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_barang_keluar_transfer` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_pengeluaran` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`tanggal` DATE NOT NULL,
	`id_pelanggan` INT(11) NULL,
	`jenis` ENUM('penjualan','retur_pembelian','transfer_keluar','penyesuaian','produksi','rusak','hilang','sample') NOT NULL COLLATE 'utf8_general_ci',
	`ref_nomor` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`keterangan` TEXT NULL COLLATE 'utf8_general_ci',
	`status` ENUM('draft','final') NULL COLLATE 'utf8_general_ci',
	`total_item` INT(11) NULL,
	`total_qty` DECIMAL(15,2) NULL,
	`created_by` INT(11) NULL,
	`finalized_by` INT(11) NULL,
	`finalized_at` DATETIME NULL,
	`created_at` TIMESTAMP NOT NULL,
	`updated_at` TIMESTAMP NOT NULL,
	`kode_pelanggan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_pelanggan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`gudang_asal_id` INT(11) NULL,
	`gudang_tujuan_id` INT(11) NULL,
	`nama_gudang_asal` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_gudang_tujuan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`tanggal_transfer` DATE NULL,
	`status_transfer` ENUM('draft','dikirim','diterima','batal') NULL COLLATE 'utf8_general_ci'
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_barang_masuk_detail
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_barang_masuk_detail` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`id_barang_masuk` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_penerimaan` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`tanggal` DATE NOT NULL,
	`status` ENUM('draft','final') NULL COLLATE 'utf8_general_ci',
	`jenis` ENUM('pembelian','retur_penjualan','bonus','titipan','penyesuaian','produksi','transfer_masuk') NOT NULL COLLATE 'utf8_general_ci',
	`kode_supplier` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_supplier` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`kode_barang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`nama_barang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`merk` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`tipe` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_gudang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`kode_satuan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_satuan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`qty_diterima` DECIMAL(12,2) NOT NULL,
	`harga_satuan` DECIMAL(15,2) NULL,
	`total_harga` DECIMAL(15,2) NULL,
	`keterangan` TEXT NULL COLLATE 'utf8_general_ci',
	`created_at` TIMESTAMP NOT NULL
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_barang_masuk_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_barang_masuk_summary` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_penerimaan` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`tanggal` DATE NOT NULL,
	`kode_supplier` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_supplier` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`jenis` ENUM('pembelian','retur_penjualan','bonus','titipan','penyesuaian','produksi','transfer_masuk') NOT NULL COLLATE 'utf8_general_ci',
	`ref_nomor` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`status` ENUM('draft','final') NULL COLLATE 'utf8_general_ci',
	`total_item` INT(11) NULL,
	`total_qty` DECIMAL(15,2) NULL,
	`keterangan` TEXT NULL COLLATE 'utf8_general_ci',
	`created_by` INT(11) NULL,
	`finalized_by` INT(11) NULL,
	`finalized_at` DATETIME NULL,
	`created_at` TIMESTAMP NOT NULL
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_barang_masuk_transfer
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_barang_masuk_transfer` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_penerimaan` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`tanggal` DATE NOT NULL,
	`id_supplier` INT(11) NULL,
	`jenis` ENUM('pembelian','retur_penjualan','bonus','titipan','penyesuaian','produksi','transfer_masuk') NOT NULL COLLATE 'utf8_general_ci',
	`ref_nomor` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`keterangan` TEXT NULL COLLATE 'utf8_general_ci',
	`status` ENUM('draft','final') NULL COLLATE 'utf8_general_ci',
	`total_item` INT(11) NULL,
	`total_qty` DECIMAL(15,2) NULL,
	`created_by` INT(11) NULL,
	`finalized_by` INT(11) NULL,
	`finalized_at` DATETIME NULL,
	`created_at` TIMESTAMP NOT NULL,
	`updated_at` TIMESTAMP NOT NULL,
	`kode_supplier` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_supplier` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`gudang_asal_id` INT(11) NULL,
	`gudang_tujuan_id` INT(11) NULL,
	`nama_gudang_asal` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_gudang_tujuan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`tanggal_transfer` DATE NULL,
	`status_transfer` ENUM('draft','dikirim','diterima','batal') NULL COLLATE 'utf8_general_ci'
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_pembelian_detail
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_pembelian_detail` (
	`id` INT(11) NOT NULL,
	`id_pembelian` INT(11) NOT NULL,
	`nomor_pembelian` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`tanggal_pembelian` DATE NULL,
	`status_pembelian` ENUM('draft','disetujui','dipesan','diterima','selesai','dibatalkan') NULL COLLATE 'utf8_general_ci',
	`jenis_pembelian` ENUM('reguler','konsinyasi','kontrak') NULL COLLATE 'utf8_general_ci',
	`kode_supplier` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_supplier` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`id_barang` INT(11) NOT NULL,
	`kode_barang` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_barang` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`merk` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`tipe` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`id_gudang` INT(11) NOT NULL,
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_gudang` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`qty` DECIMAL(10,2) NOT NULL,
	`kode_satuan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_satuan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`harga_satuan` DECIMAL(15,2) NOT NULL,
	`diskon_persen` DECIMAL(5,2) NULL,
	`diskon_nominal` DECIMAL(15,2) NULL,
	`subtotal_sebelum_diskon` DECIMAL(15,2) NOT NULL,
	`subtotal_setelah_diskon` DECIMAL(15,2) NOT NULL,
	`ppn_persen` DECIMAL(5,2) NULL,
	`ppn_nominal` DECIMAL(15,2) NULL,
	`total_akhir` DECIMAL(15,2) NOT NULL,
	`keterangan` TEXT NULL COLLATE 'utf8_general_ci'
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_pembelian_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_pembelian_summary` (
	`id` INT(11) NOT NULL,
	`nomor_pembelian` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`tanggal_pembelian` DATE NOT NULL,
	`id_supplier` INT(11) NOT NULL,
	`kode_supplier` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_supplier` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`alamat_supplier` TEXT NULL COLLATE 'utf8_general_ci',
	`telepon_supplier` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`email_supplier` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`jenis_pembelian` ENUM('reguler','konsinyasi','kontrak') NOT NULL COLLATE 'utf8_general_ci',
	`status` ENUM('draft','disetujui','dipesan','diterima','selesai','dibatalkan') NOT NULL COLLATE 'utf8_general_ci',
	`total_item` INT(11) NOT NULL,
	`total_qty` DECIMAL(10,2) NOT NULL,
	`subtotal` DECIMAL(15,2) NOT NULL,
	`diskon_persen` DECIMAL(5,2) NULL,
	`diskon_nominal` DECIMAL(15,2) NULL,
	`total_sebelum_pajak` DECIMAL(15,2) NOT NULL,
	`ppn_persen` DECIMAL(5,2) NULL,
	`ppn_nominal` DECIMAL(15,2) NULL,
	`total_setelah_pajak` DECIMAL(15,2) NOT NULL,
	`biaya_pengiriman` DECIMAL(15,2) NULL,
	`total_akhir` DECIMAL(15,2) NOT NULL,
	`tanggal_jatuh_tempo` DATE NULL,
	`syarat_pembayaran` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`metode_pembayaran` ENUM('tunai','transfer','kredit','cek','giro') NULL COLLATE 'utf8_general_ci',
	`nomor_po_supplier` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`alamat_pengiriman` TEXT NULL COLLATE 'utf8_general_ci',
	`keterangan` TEXT NULL COLLATE 'utf8_general_ci',
	`created_at` TIMESTAMP NOT NULL,
	`updated_at` TIMESTAMP NOT NULL,
	`created_by` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`updated_by` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`approved_by` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`approved_at` DATETIME NULL,
	`total_dibayar` DECIMAL(37,2) NULL,
	`sisa_pembayaran` DECIMAL(38,2) NULL,
	`status_pembayaran` VARCHAR(1) NULL COLLATE 'utf8mb4_general_ci'
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_pengiriman_detail
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_pengiriman_detail` 
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_pengiriman_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_pengiriman_summary` 
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_stok_movement_detail
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_stok_movement_detail` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`tanggal` DATETIME NOT NULL,
	`kode_barang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`nama_barang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_gudang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`tipe_transaksi` ENUM('pembelian','penjualan','retur_beli','retur_jual','penyesuaian','opname','transfer_masuk','transfer_keluar') NOT NULL COLLATE 'utf8_general_ci',
	`qty_in` DECIMAL(12,2) NULL,
	`qty_out` DECIMAL(12,2) NULL,
	`keterangan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`ref_transaksi` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`user_input` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`created_at` TIMESTAMP NOT NULL
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_stok_opname_detail
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_stok_opname_detail` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`id_opname` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_opname` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`tanggal_opname` DATE NOT NULL,
	`status` ENUM('draft','final') NULL COLLATE 'utf8_general_ci',
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_gudang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`kode_barang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`nama_barang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`qty_sistem` DECIMAL(12,2) NOT NULL,
	`qty_fisik` DECIMAL(12,2) NOT NULL,
	`selisih` DECIMAL(12,2) NULL,
	`keterangan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`created_at` TIMESTAMP NOT NULL
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_stok_opname_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_stok_opname_summary` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_opname` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`tanggal_opname` DATE NOT NULL,
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_gudang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`status` ENUM('draft','final') NULL COLLATE 'utf8_general_ci',
	`total_item` INT(11) NULL,
	`total_selisih_positif` DECIMAL(15,2) NULL,
	`total_selisih_negatif` DECIMAL(15,2) NULL,
	`keterangan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`user_input` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`user_final` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`tanggal_final` DATETIME NULL,
	`created_at` TIMESTAMP NOT NULL
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_stok_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_stok_summary` (
	`id_barang` INT(11) NOT NULL,
	`kode_barang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`nama_barang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`id_gudang` INT(11) NOT NULL,
	`kode_gudang` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_gudang` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`qty_terakhir` DECIMAL(12,2) NOT NULL,
	`updated_at` TIMESTAMP NOT NULL
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_transfer_stok_detail
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_transfer_stok_detail` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`transfer_stok_id` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_transfer` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`tanggal_transfer` DATE NULL,
	`status` ENUM('draft','dikirim','diterima','batal') NULL COLLATE 'utf8_general_ci',
	`barang_id` INT(11) NOT NULL,
	`kode_barang` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_barang` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`qty` DECIMAL(18,2) NOT NULL,
	`kode_satuan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_satuan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_gudang_asal` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_gudang_tujuan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`keterangan` TEXT NULL COLLATE 'utf8_general_ci'
) ENGINE=MyISAM;

-- Dumping structure for view toko_elektronik.v_transfer_stok_summary
-- Creating temporary table to overcome VIEW dependency errors
CREATE TABLE `v_transfer_stok_summary` (
	`id` BIGINT(20) UNSIGNED NOT NULL,
	`nomor_transfer` VARCHAR(1) NOT NULL COLLATE 'utf8_general_ci',
	`tanggal_transfer` DATE NOT NULL,
	`status` ENUM('draft','dikirim','diterima','batal') NULL COLLATE 'utf8_general_ci',
	`total_item` INT(11) NULL,
	`total_qty` DECIMAL(15,2) NULL,
	`kode_gudang_asal` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_gudang_asal` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`kode_gudang_tujuan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`nama_gudang_tujuan` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`keterangan` TEXT NULL COLLATE 'utf8_general_ci',
	`tanggal_kirim` DATETIME NULL,
	`tanggal_terima` DATETIME NULL,
	`dibuat_oleh` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`dikirim_oleh` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`diterima_oleh` VARCHAR(1) NULL COLLATE 'utf8_general_ci',
	`dibuat_pada` DATETIME NULL
) ENGINE=MyISAM;

-- Dumping structure for trigger toko_elektronik.trg_finalize_barang_keluar
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_finalize_barang_keluar` AFTER UPDATE ON `barang_keluar` FOR EACH ROW BEGIN
    
    IF OLD.status = 'draft' AND NEW.status = 'final' THEN
        
        INSERT INTO stok_movement (
            tanggal, id_barang, id_gudang, tipe_transaksi,
            qty_in, qty_out, keterangan, ref_transaksi, user_input
        )
        SELECT
            CONCAT(NEW.tanggal, ' ', TIME(NOW())),
            bkd.id_barang,
            bkd.id_gudang,
            CASE NEW.jenis
                WHEN 'penjualan' THEN 'penjualan'
                WHEN 'retur_pembelian' THEN 'retur_beli'
                WHEN 'transfer_keluar' THEN 'transfer_keluar'
                ELSE 'penyesuaian'
            END,
            0, 
            bkd.qty_keluar, 
            CONCAT('Barang Keluar: ', COALESCE(bkd.keterangan, NEW.keterangan, '')),
            NEW.nomor_pengeluaran,
            CONCAT('user_', COALESCE(NEW.finalized_by, NEW.created_by, 0))
        FROM barang_keluar_detail bkd
        WHERE bkd.id_barang_keluar = NEW.id;
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_finalize_barang_masuk
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_finalize_barang_masuk` AFTER UPDATE ON `barang_masuk` FOR EACH ROW BEGIN
    -- Hanya jalankan jika status berubah dari draft ke final
    IF OLD.status = 'draft' AND NEW.status = 'final' THEN
        -- Insert semua detail barang masuk ke stok_movement
        INSERT INTO stok_movement (
            tanggal, id_barang, id_gudang, tipe_transaksi,
            qty_in, qty_out, keterangan, ref_transaksi, user_input
        )
        SELECT 
            CONCAT(NEW.tanggal, ' ', TIME(NOW())),
            bmd.id_barang,
            bmd.id_gudang,
            CASE 
                WHEN NEW.jenis = 'pembelian' THEN 'pembelian'
                WHEN NEW.jenis = 'retur_penjualan' THEN 'retur_jual'
                WHEN NEW.jenis = 'transfer_masuk' THEN 'transfer_masuk'
                ELSE 'pembelian'
            END,
            bmd.qty_diterima,
            0,
            CONCAT('Barang Masuk - ', NEW.jenis, ': ', COALESCE(bmd.keterangan, NEW.keterangan)),
            NEW.nomor_penerimaan,
            CONCAT('user_', COALESCE(NEW.finalized_by, NEW.created_by))
        FROM barang_masuk_detail bmd
        WHERE bmd.id_barang_masuk = NEW.id
        AND bmd.qty_diterima > 0;
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_finalize_opname
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_finalize_opname` AFTER UPDATE ON `stok_opname` FOR EACH ROW BEGIN
    
    IF OLD.status = 'draft' AND NEW.status = 'final' THEN
        
        INSERT INTO stok_movement (
            tanggal, id_barang, id_gudang, tipe_transaksi,
            qty_in, qty_out, keterangan, ref_transaksi, user_input
        )
        SELECT 
            CONCAT(NEW.tanggal_opname, ' ', TIME(NOW())),
            sod.id_barang,
            NEW.id_gudang,
            'opname',
            CASE WHEN sod.selisih > 0 THEN sod.selisih ELSE 0 END,
            CASE WHEN sod.selisih < 0 THEN ABS(sod.selisih) ELSE 0 END,
            CONCAT('Stock Opname: ', COALESCE(sod.keterangan, NEW.keterangan)),
            NEW.nomor_opname,
            NEW.user_final
        FROM stok_opname_detail sod
        WHERE sod.id_opname = NEW.id
        AND sod.selisih != 0; 
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_pembelian_status_tracking
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER `trg_pembelian_status_tracking` AFTER UPDATE ON `pembelian` FOR EACH ROW
BEGIN
    -- Jika status berubah, catat di tracking
    IF OLD.status != NEW.status THEN
        INSERT INTO pembelian_tracking (
            id_pembelian, 
            status, 
            tanggal_status, 
            keterangan, 
            created_by
        ) VALUES (
            NEW.id,
            NEW.status,
            NOW(),
            CONCAT('Status berubah dari ', OLD.status, ' menjadi ', NEW.status),
            NEW.updated_by
        );
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_penyesuaian_to_movement
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_penyesuaian_to_movement` AFTER INSERT ON `penyesuaian_stok` FOR EACH ROW BEGIN
    DECLARE qty_in_val DECIMAL(12,2) DEFAULT 0;
    DECLARE qty_out_val DECIMAL(12,2) DEFAULT 0;
    
    
    IF NEW.jenis_penyesuaian = 'PENAMBAHAN' THEN
        SET qty_in_val = NEW.qty_selisih;
        SET qty_out_val = 0;
    ELSE
        SET qty_in_val = 0;
        SET qty_out_val = ABS(NEW.qty_selisih);
    END IF;
    
    
    INSERT INTO stok_movement (
        tanggal, id_barang, id_gudang, tipe_transaksi,
        qty_in, qty_out, keterangan, ref_transaksi, user_input
    ) VALUES (
        CONCAT(NEW.tanggal, ' ', TIME(NOW())), 
        NEW.id_barang, 
        NEW.id_gudang, 
        'penyesuaian',
        qty_in_val,
        qty_out_val,
        NEW.alasan,
        NEW.kode_penyesuaian,
        CONCAT('user_', COALESCE(NEW.user_id, 0))
    );
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_transfer_kirim
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_transfer_kirim` AFTER UPDATE ON `transfer_stok` FOR EACH ROW 
BEGIN
    DECLARE nomor_keluar VARCHAR(50);
    DECLARE user_id INT DEFAULT 1;
    DECLARE detail_count INT DEFAULT 0;
    DECLARE barang_keluar_id BIGINT;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        -- Log error jika terjadi masalah
        INSERT INTO error_log (table_name, error_message, created_at) 
        VALUES ('transfer_stok', CONCAT('Error in trg_transfer_kirim for ID: ', NEW.id), NOW())
        ON DUPLICATE KEY UPDATE error_message = VALUES(error_message);
        RESIGNAL;
    END;

    -- Hanya jalankan jika status berubah dari draft ke dikirim
    IF OLD.status = 'draft' AND NEW.status = 'dikirim' THEN
        -- Cek apakah ada detail transfer
        SELECT COUNT(*) INTO detail_count 
        FROM transfer_stok_detail 
        WHERE transfer_stok_id = NEW.id;
        
        IF detail_count > 0 THEN
            -- Generate nomor barang keluar
            SET nomor_keluar = CONCAT('TK-', DATE_FORMAT(NEW.tanggal_kirim, '%Y%m%d'), '-', LPAD(NEW.id, 4, '0'));

            -- Cari user ID berdasarkan nama yang mengirim
            SELECT id_user INTO user_id 
            FROM tbl_user 
            WHERE full_name = NEW.dikirim_oleh OR username = NEW.dikirim_oleh 
            LIMIT 1;
            
            IF user_id IS NULL THEN
                SET user_id = 1; 
            END IF;

            -- Insert barang keluar header dengan status final
            INSERT INTO barang_keluar (
                nomor_pengeluaran, tanggal, jenis, ref_nomor, keterangan,
                status, total_item, total_qty, created_by, finalized_by, finalized_at
            ) VALUES (
                nomor_keluar,
                DATE(NEW.tanggal_kirim),
                'transfer_keluar',
                NEW.nomor_transfer,
                CONCAT('Transfer ke ', (SELECT nama_gudang FROM gudang WHERE id = NEW.gudang_tujuan_id)),
                'final',
                NEW.total_item,
                NEW.total_qty,
                user_id,
                user_id,
                NEW.tanggal_kirim
            );

            -- Ambil ID barang keluar yang baru dibuat
            SET barang_keluar_id = LAST_INSERT_ID();

            -- Insert detail barang keluar
            INSERT INTO barang_keluar_detail (
                id_barang_keluar, id_barang, id_gudang, qty_keluar, id_satuan, keterangan
            )
            SELECT
                barang_keluar_id,
                tsd.barang_id,
                NEW.gudang_asal_id,
                tsd.qty,
                tsd.satuan_id,
                CONCAT('Transfer ke gudang ', (SELECT nama_gudang FROM gudang WHERE id = NEW.gudang_tujuan_id), ' - ', COALESCE(tsd.keterangan, ''))
            FROM transfer_stok_detail tsd
            WHERE tsd.transfer_stok_id = NEW.id
            AND tsd.qty > 0;
        END IF;
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_transfer_terima
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_transfer_terima` AFTER UPDATE ON `transfer_stok` FOR EACH ROW 
BEGIN
    DECLARE nomor_masuk VARCHAR(50);
    DECLARE user_id INT DEFAULT 1;
    DECLARE detail_count INT DEFAULT 0;
    DECLARE barang_masuk_id BIGINT;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        -- Log error jika terjadi masalah
        INSERT INTO error_log (table_name, error_message, created_at) 
        VALUES ('transfer_stok', CONCAT('Error in trg_transfer_terima for ID: ', NEW.id), NOW())
        ON DUPLICATE KEY UPDATE error_message = VALUES(error_message);
        RESIGNAL;
    END;

    -- Hanya jalankan jika status berubah dari dikirim ke diterima
    IF OLD.status = 'dikirim' AND NEW.status = 'diterima' THEN
        -- Cek apakah ada detail transfer
        SELECT COUNT(*) INTO detail_count 
        FROM transfer_stok_detail 
        WHERE transfer_stok_id = NEW.id;
        
        IF detail_count > 0 THEN
            -- Generate nomor barang masuk
            SET nomor_masuk = CONCAT('TM-', DATE_FORMAT(NEW.tanggal_terima, '%Y%m%d'), '-', LPAD(NEW.id, 4, '0'));

            -- Cari user ID berdasarkan nama yang diterima
            SELECT id_user INTO user_id 
            FROM tbl_user 
            WHERE full_name = NEW.diterima_oleh OR username = NEW.diterima_oleh 
            LIMIT 1;
            
            IF user_id IS NULL THEN
                SET user_id = 1; 
            END IF;

            -- Insert barang masuk header dengan status final
            INSERT INTO barang_masuk (
                nomor_penerimaan, tanggal, jenis, ref_nomor, keterangan,
                status, total_item, total_qty, created_by, finalized_by, finalized_at
            ) VALUES (
                nomor_masuk,
                DATE(NEW.tanggal_terima),
                'transfer_masuk',
                NEW.nomor_transfer,
                CONCAT('Transfer dari ', (SELECT nama_gudang FROM gudang WHERE id = NEW.gudang_asal_id)),
                'final',
                NEW.total_item,
                NEW.total_qty,
                user_id,
                user_id,
                NEW.tanggal_terima
            );

            -- Ambil ID barang masuk yang baru dibuat
            SET barang_masuk_id = LAST_INSERT_ID();

            -- Insert detail barang masuk
            INSERT INTO barang_masuk_detail (
                id_barang_masuk, id_barang, id_gudang, qty_diterima, id_satuan, keterangan
            )
            SELECT
                barang_masuk_id,
                tsd.barang_id,
                NEW.gudang_tujuan_id,
                tsd.qty,
                tsd.satuan_id,
                CONCAT('Transfer dari gudang ', (SELECT nama_gudang FROM gudang WHERE id = NEW.gudang_asal_id), ' - ', COALESCE(tsd.keterangan, ''))
            FROM transfer_stok_detail tsd
            WHERE tsd.transfer_stok_id = NEW.id
            AND tsd.qty > 0;
        END IF;
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_barang_keluar_summary_delete
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_barang_keluar_summary_delete` AFTER DELETE ON `barang_keluar_detail` FOR EACH ROW BEGIN
    UPDATE barang_keluar 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = OLD.id_barang_keluar
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_keluar), 0) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = OLD.id_barang_keluar
        )
    WHERE id = OLD.id_barang_keluar;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_barang_keluar_summary_insert
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_barang_keluar_summary_insert` AFTER INSERT ON `barang_keluar_detail` FOR EACH ROW BEGIN
    UPDATE barang_keluar 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = NEW.id_barang_keluar
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_keluar), 0) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = NEW.id_barang_keluar
        )
    WHERE id = NEW.id_barang_keluar;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_barang_keluar_summary_update
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_barang_keluar_summary_update` AFTER UPDATE ON `barang_keluar_detail` FOR EACH ROW BEGIN
    UPDATE barang_keluar 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = NEW.id_barang_keluar
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_keluar), 0) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = NEW.id_barang_keluar
        )
    WHERE id = NEW.id_barang_keluar;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_barang_masuk_summary_delete
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_barang_masuk_summary_delete` AFTER DELETE ON `barang_masuk_detail` FOR EACH ROW BEGIN
    UPDATE barang_masuk SET
        total_item = (
            SELECT COUNT(*) FROM barang_masuk_detail 
            WHERE id_barang_masuk = OLD.id_barang_masuk
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_diterima), 0) FROM barang_masuk_detail 
            WHERE id_barang_masuk = OLD.id_barang_masuk
        )
    WHERE id = OLD.id_barang_masuk;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_barang_masuk_summary_insert
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_barang_masuk_summary_insert` AFTER INSERT ON `barang_masuk_detail` FOR EACH ROW BEGIN
    UPDATE barang_masuk SET
        total_item = (
            SELECT COUNT(*) FROM barang_masuk_detail 
            WHERE id_barang_masuk = NEW.id_barang_masuk
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_diterima), 0) FROM barang_masuk_detail 
            WHERE id_barang_masuk = NEW.id_barang_masuk
        )
    WHERE id = NEW.id_barang_masuk;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_barang_masuk_summary_update
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_barang_masuk_summary_update` AFTER UPDATE ON `barang_masuk_detail` FOR EACH ROW BEGIN
    UPDATE barang_masuk SET
        total_item = (
            SELECT COUNT(*) FROM barang_masuk_detail 
            WHERE id_barang_masuk = NEW.id_barang_masuk
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_diterima), 0) FROM barang_masuk_detail 
            WHERE id_barang_masuk = NEW.id_barang_masuk
        )
    WHERE id = NEW.id_barang_masuk;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_opname_summary_delete
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_opname_summary_delete` AFTER DELETE ON `stok_opname_detail` FOR EACH ROW BEGIN
    UPDATE stok_opname SET
        total_item = (
            SELECT COUNT(*) FROM stok_opname_detail 
            WHERE id_opname = OLD.id_opname
        ),
        total_selisih_positif = (
            SELECT COALESCE(SUM(selisih), 0) FROM stok_opname_detail 
            WHERE id_opname = OLD.id_opname AND selisih > 0
        ),
        total_selisih_negatif = (
            SELECT COALESCE(ABS(SUM(selisih)), 0) FROM stok_opname_detail 
            WHERE id_opname = OLD.id_opname AND selisih < 0
        )
    WHERE id = OLD.id_opname;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_opname_summary_insert
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_opname_summary_insert` AFTER INSERT ON `stok_opname_detail` FOR EACH ROW BEGIN
    UPDATE stok_opname SET
        total_item = (
            SELECT COUNT(*) FROM stok_opname_detail 
            WHERE id_opname = NEW.id_opname
        ),
        total_selisih_positif = (
            SELECT COALESCE(SUM(selisih), 0) FROM stok_opname_detail 
            WHERE id_opname = NEW.id_opname AND selisih > 0
        ),
        total_selisih_negatif = (
            SELECT COALESCE(ABS(SUM(selisih)), 0) FROM stok_opname_detail 
            WHERE id_opname = NEW.id_opname AND selisih < 0
        )
    WHERE id = NEW.id_opname;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_opname_summary_update
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_opname_summary_update` AFTER UPDATE ON `stok_opname_detail` FOR EACH ROW BEGIN
    UPDATE stok_opname SET
        total_item = (
            SELECT COUNT(*) FROM stok_opname_detail 
            WHERE id_opname = NEW.id_opname
        ),
        total_selisih_positif = (
            SELECT COALESCE(SUM(selisih), 0) FROM stok_opname_detail 
            WHERE id_opname = NEW.id_opname AND selisih > 0
        ),
        total_selisih_negatif = (
            SELECT COALESCE(ABS(SUM(selisih)), 0) FROM stok_opname_detail 
            WHERE id_opname = NEW.id_opname AND selisih < 0
        )
    WHERE id = NEW.id_opname;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_pembelian_totals_delete
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER `trg_update_pembelian_totals_delete` AFTER DELETE ON `pembelian_detail` FOR EACH ROW
BEGIN
    CALL sp_update_pembelian_totals(OLD.id_pembelian);
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_pembelian_totals_insert
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER `trg_update_pembelian_totals_insert` AFTER INSERT ON `pembelian_detail` FOR EACH ROW
BEGIN
    CALL sp_update_pembelian_totals(NEW.id_pembelian);
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_pembelian_totals_update
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_ZERO_IN_DATE,NO_ZERO_DATE,NO_ENGINE_SUBSTITUTION';
DELIMITER //
CREATE TRIGGER `trg_update_pembelian_totals_update` AFTER UPDATE ON `pembelian_detail` FOR EACH ROW
BEGIN
    CALL sp_update_pembelian_totals(NEW.id_pembelian);
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_stok_barang
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_stok_barang` AFTER INSERT ON `stok_movement` FOR EACH ROW BEGIN
    DECLARE total_qty DECIMAL(12,2);
    
    
    IF EXISTS (
        SELECT 1 FROM stok_barang
        WHERE id_barang = NEW.id_barang AND id_gudang = NEW.id_gudang
    ) THEN
        
        UPDATE stok_barang
        SET qty_terakhir = qty_terakhir + NEW.qty_in - NEW.qty_out
        WHERE id_barang = NEW.id_barang AND id_gudang = NEW.id_gudang;
    ELSE
        
        INSERT INTO stok_barang (id_barang, id_gudang, qty_terakhir)
        VALUES (NEW.id_barang, NEW.id_gudang, NEW.qty_in - NEW.qty_out);
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_transfer_totals_delete
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_transfer_totals_delete` AFTER DELETE ON `transfer_stok_detail` FOR EACH ROW BEGIN
    UPDATE transfer_stok
    SET
        total_item = (
            SELECT COUNT(*)
            FROM transfer_stok_detail
            WHERE transfer_stok_id = OLD.transfer_stok_id
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty), 0)
            FROM transfer_stok_detail
            WHERE transfer_stok_id = OLD.transfer_stok_id
        )
    WHERE id = OLD.transfer_stok_id;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_transfer_totals_insert
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_transfer_totals_insert` AFTER INSERT ON `transfer_stok_detail` FOR EACH ROW BEGIN
    UPDATE transfer_stok
    SET
        total_item = (
            SELECT COUNT(*)
            FROM transfer_stok_detail
            WHERE transfer_stok_id = NEW.transfer_stok_id
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty), 0)
            FROM transfer_stok_detail
            WHERE transfer_stok_id = NEW.transfer_stok_id
        )
    WHERE id = NEW.transfer_stok_id;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_update_transfer_totals_update
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_update_transfer_totals_update` AFTER UPDATE ON `transfer_stok_detail` FOR EACH ROW BEGIN
    UPDATE transfer_stok
    SET
        total_item = (
            SELECT COUNT(*)
            FROM transfer_stok_detail
            WHERE transfer_stok_id = NEW.transfer_stok_id
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty), 0)
            FROM transfer_stok_detail
            WHERE transfer_stok_id = NEW.transfer_stok_id
        )
    WHERE id = NEW.transfer_stok_id;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_validate_transfer_keluar_ref
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_validate_transfer_keluar_ref` BEFORE INSERT ON `barang_keluar` FOR EACH ROW BEGIN
    
    IF NEW.jenis = 'transfer_keluar' THEN
        IF NEW.ref_nomor IS NULL OR NEW.ref_nomor NOT LIKE 'TR-%' THEN
            SIGNAL SQLSTATE '45000' 
            SET MESSAGE_TEXT = 'Transfer keluar hanya bisa dibuat melalui modul Transfer Stok';
        END IF;
        
        
        
        IF NOT EXISTS (
            SELECT 1 FROM transfer_stok 
            WHERE nomor_transfer = NEW.ref_nomor 
            AND status IN ('draft', 'dikirim')
        ) THEN
            SIGNAL SQLSTATE '45000' 
            SET MESSAGE_TEXT = 'Nomor transfer tidak valid atau tidak dalam status yang sesuai';
        END IF;
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Dumping structure for trigger toko_elektronik.trg_validate_transfer_masuk_ref
SET @OLDTMP_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';
DELIMITER //
CREATE TRIGGER `trg_validate_transfer_masuk_ref` BEFORE INSERT ON `barang_masuk` FOR EACH ROW BEGIN
    -- Jika jenis adalah transfer_masuk, pastikan ref_nomor dimulai dengan 'TR-'
    IF NEW.jenis = 'transfer_masuk' THEN
        IF NEW.ref_nomor IS NULL OR NEW.ref_nomor NOT LIKE 'TR-%' THEN
            SIGNAL SQLSTATE '45000' 
            SET MESSAGE_TEXT = 'Transfer masuk hanya bisa dibuat melalui modul Transfer Stok';
        END IF;
        
        -- Pastikan nomor transfer ada di tabel transfer_stok
        IF NOT EXISTS (
            SELECT 1 FROM transfer_stok 
            WHERE nomor_transfer = NEW.ref_nomor 
            -- AND status = 'dikirim'
            AND status = 'diterima'
        ) THEN
            SIGNAL SQLSTATE '45000' 
            -- SET MESSAGE_TEXT = 'Nomor transfer tidak valid atau belum dalam status dikirim';
            SET MESSAGE_TEXT = 'Nomor transfer tidak valid atau statusnya bukan ''diterima''.'; -- Updated message
        END IF;
    END IF;
END//
DELIMITER ;
SET SQL_MODE=@OLDTMP_SQL_MODE;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_barang_keluar_detail`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_barang_keluar_detail` AS SELECT `bkd`.`id` AS `id`, `bkd`.`id_barang_keluar` AS `id_barang_keluar`, `bk`.`nomor_pengeluaran` AS `nomor_pengeluaran`, `bk`.`tanggal` AS `tanggal`, `bk`.`jenis` AS `jenis`, `bk`.`status` AS `status`, `b`.`kode_barang` AS `kode_barang`, `b`.`nama_barang` AS `nama_barang`, `g`.`kode_gudang` AS `kode_gudang`, `g`.`nama_gudang` AS `nama_gudang`, `bkd`.`qty_keluar` AS `qty_keluar`, `s`.`kode_satuan` AS `kode_satuan`, `s`.`nama_satuan` AS `nama_satuan`, `bkd`.`harga_satuan` AS `harga_satuan`, `bkd`.`total_harga` AS `total_harga`, `bkd`.`keterangan` AS `keterangan`, `bkd`.`created_at` AS `created_at` FROM ((((`barang_keluar_detail` `bkd` join `barang_keluar` `bk` on(`bkd`.`id_barang_keluar` = `bk`.`id`)) join `barang` `b` on(`bkd`.`id_barang` = `b`.`id`)) join `gudang` `g` on(`bkd`.`id_gudang` = `g`.`id`)) left join `satuan` `s` on(`bkd`.`id_satuan` = `s`.`id`)) ORDER BY `bk`.`tanggal` DESC, `b`.`nama_barang` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_barang_keluar_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_barang_keluar_summary` AS SELECT `bk`.`id` AS `id`, `bk`.`nomor_pengeluaran` AS `nomor_pengeluaran`, `bk`.`tanggal` AS `tanggal`, `bk`.`jenis` AS `jenis`, `bk`.`ref_nomor` AS `ref_nomor`, `bk`.`status` AS `status`, `bk`.`total_item` AS `total_item`, `bk`.`total_qty` AS `total_qty`, `p`.`kode` AS `kode_pelanggan`, `p`.`nama` AS `nama_pelanggan`, `bk`.`keterangan` AS `keterangan`, `bk`.`created_by` AS `created_by`, `bk`.`finalized_by` AS `finalized_by`, `bk`.`finalized_at` AS `finalized_at`, `bk`.`created_at` AS `created_at`, `bk`.`updated_at` AS `updated_at` FROM (`barang_keluar` `bk` left join `pelanggan` `p` on(`bk`.`id_pelanggan` = `p`.`id`)) ORDER BY `bk`.`tanggal` DESC, `bk`.`id` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_barang_keluar_transfer`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_barang_keluar_transfer` AS SELECT `bk`.`id` AS `id`, `bk`.`nomor_pengeluaran` AS `nomor_pengeluaran`, `bk`.`tanggal` AS `tanggal`, `bk`.`id_pelanggan` AS `id_pelanggan`, `bk`.`jenis` AS `jenis`, `bk`.`ref_nomor` AS `ref_nomor`, `bk`.`keterangan` AS `keterangan`, `bk`.`status` AS `status`, `bk`.`total_item` AS `total_item`, `bk`.`total_qty` AS `total_qty`, `bk`.`created_by` AS `created_by`, `bk`.`finalized_by` AS `finalized_by`, `bk`.`finalized_at` AS `finalized_at`, `bk`.`created_at` AS `created_at`, `bk`.`updated_at` AS `updated_at`, `p`.`kode` AS `kode_pelanggan`, `p`.`nama` AS `nama_pelanggan`, `ts`.`gudang_asal_id` AS `gudang_asal_id`, `ts`.`gudang_tujuan_id` AS `gudang_tujuan_id`, `ga`.`nama_gudang` AS `nama_gudang_asal`, `gt`.`nama_gudang` AS `nama_gudang_tujuan`, `ts`.`tanggal_transfer` AS `tanggal_transfer`, `ts`.`status` AS `status_transfer` FROM ((((`barang_keluar` `bk` left join `pelanggan` `p` on(`bk`.`id_pelanggan` = `p`.`id`)) left join `transfer_stok` `ts` on(`bk`.`ref_nomor` = `ts`.`nomor_transfer`)) left join `gudang` `ga` on(`ts`.`gudang_asal_id` = `ga`.`id`)) left join `gudang` `gt` on(`ts`.`gudang_tujuan_id` = `gt`.`id`)) WHERE `bk`.`jenis` = 'transfer_keluar' ORDER BY `bk`.`id` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_barang_masuk_detail`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_barang_masuk_detail` AS SELECT `bmd`.`id` AS `id`, `bmd`.`id_barang_masuk` AS `id_barang_masuk`, `bm`.`nomor_penerimaan` AS `nomor_penerimaan`, `bm`.`tanggal` AS `tanggal`, `bm`.`status` AS `status`, `bm`.`jenis` AS `jenis`, `s`.`kode` AS `kode_supplier`, `s`.`nama` AS `nama_supplier`, `b`.`kode_barang` AS `kode_barang`, `b`.`nama_barang` AS `nama_barang`, `b`.`merk` AS `merk`, `b`.`tipe` AS `tipe`, `g`.`kode_gudang` AS `kode_gudang`, `g`.`nama_gudang` AS `nama_gudang`, `sat`.`kode_satuan` AS `kode_satuan`, `sat`.`nama_satuan` AS `nama_satuan`, `bmd`.`qty_diterima` AS `qty_diterima`, `bmd`.`harga_satuan` AS `harga_satuan`, `bmd`.`total_harga` AS `total_harga`, `bmd`.`keterangan` AS `keterangan`, `bmd`.`created_at` AS `created_at` FROM (((((`barang_masuk_detail` `bmd` join `barang_masuk` `bm` on(`bmd`.`id_barang_masuk` = `bm`.`id`)) left join `supplier` `s` on(`bm`.`id_supplier` = `s`.`id`)) join `barang` `b` on(`bmd`.`id_barang` = `b`.`id`)) join `gudang` `g` on(`bmd`.`id_gudang` = `g`.`id`)) left join `satuan` `sat` on(`bmd`.`id_satuan` = `sat`.`id`)) ORDER BY `bm`.`tanggal` DESC, `b`.`nama_barang` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_barang_masuk_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_barang_masuk_summary` AS SELECT `bm`.`id` AS `id`, `bm`.`nomor_penerimaan` AS `nomor_penerimaan`, `bm`.`tanggal` AS `tanggal`, `s`.`kode` AS `kode_supplier`, `s`.`nama` AS `nama_supplier`, `bm`.`jenis` AS `jenis`, `bm`.`ref_nomor` AS `ref_nomor`, `bm`.`status` AS `status`, `bm`.`total_item` AS `total_item`, `bm`.`total_qty` AS `total_qty`, `bm`.`keterangan` AS `keterangan`, `bm`.`created_by` AS `created_by`, `bm`.`finalized_by` AS `finalized_by`, `bm`.`finalized_at` AS `finalized_at`, `bm`.`created_at` AS `created_at` FROM (`barang_masuk` `bm` left join `supplier` `s` on(`bm`.`id_supplier` = `s`.`id`)) ORDER BY `bm`.`tanggal` DESC, `bm`.`id` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_barang_masuk_transfer`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_barang_masuk_transfer` AS SELECT `bm`.`id` AS `id`, `bm`.`nomor_penerimaan` AS `nomor_penerimaan`, `bm`.`tanggal` AS `tanggal`, `bm`.`id_supplier` AS `id_supplier`, `bm`.`jenis` AS `jenis`, `bm`.`ref_nomor` AS `ref_nomor`, `bm`.`keterangan` AS `keterangan`, `bm`.`status` AS `status`, `bm`.`total_item` AS `total_item`, `bm`.`total_qty` AS `total_qty`, `bm`.`created_by` AS `created_by`, `bm`.`finalized_by` AS `finalized_by`, `bm`.`finalized_at` AS `finalized_at`, `bm`.`created_at` AS `created_at`, `bm`.`updated_at` AS `updated_at`, `s`.`kode` AS `kode_supplier`, `s`.`nama` AS `nama_supplier`, `ts`.`gudang_asal_id` AS `gudang_asal_id`, `ts`.`gudang_tujuan_id` AS `gudang_tujuan_id`, `ga`.`nama_gudang` AS `nama_gudang_asal`, `gt`.`nama_gudang` AS `nama_gudang_tujuan`, `ts`.`tanggal_transfer` AS `tanggal_transfer`, `ts`.`status` AS `status_transfer` FROM ((((`barang_masuk` `bm` left join `supplier` `s` on(`bm`.`id_supplier` = `s`.`id`)) left join `transfer_stok` `ts` on(`bm`.`ref_nomor` = `ts`.`nomor_transfer`)) left join `gudang` `ga` on(`ts`.`gudang_asal_id` = `ga`.`id`)) left join `gudang` `gt` on(`ts`.`gudang_tujuan_id` = `gt`.`id`)) WHERE `bm`.`jenis` = 'transfer_masuk' ORDER BY `bm`.`id` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_pembelian_detail`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_pembelian_detail` AS SELECT 
    pd.id,
    pd.id_pembelian,
    p.nomor_pembelian,
    p.tanggal_pembelian,
    p.status as status_pembelian,
    p.jenis_pembelian,
    s.kode as kode_supplier,
    s.nama as nama_supplier,
    pd.id_barang,
    b.kode_barang,
    b.nama_barang,
    b.merk,
    b.tipe,
    pd.id_gudang,
    g.kode_gudang,
    g.nama_gudang,
    pd.qty,
    sat.kode_satuan,
    sat.nama_satuan,
    pd.harga_satuan,
    pd.diskon_persen,
    pd.diskon_nominal,
    pd.subtotal_sebelum_diskon,
    pd.subtotal_setelah_diskon,
    pd.ppn_persen,
    pd.ppn_nominal,
    pd.total_akhir,
    pd.keterangan
FROM pembelian_detail pd
LEFT JOIN pembelian p ON pd.id_pembelian = p.id
LEFT JOIN supplier s ON p.id_supplier = s.id
LEFT JOIN barang b ON pd.id_barang = b.id
LEFT JOIN gudang g ON pd.id_gudang = g.id
LEFT JOIN satuan sat ON b.satuan_id = sat.id
ORDER BY p.tanggal_pembelian DESC, b.nama_barang ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_pembelian_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_pembelian_summary` AS SELECT 
    p.id,
    p.nomor_pembelian,
    p.tanggal_pembelian,
    p.id_supplier,
    s.kode as kode_supplier,
    s.nama as nama_supplier,
    s.alamat as alamat_supplier,
    s.no_telepon as telepon_supplier,
    s.email as email_supplier,
    p.jenis_pembelian,
    p.status,
    p.total_item,
    p.total_qty,
    p.subtotal,
    p.diskon_persen,
    p.diskon_nominal,
    p.total_sebelum_pajak,
    p.ppn_persen,
    p.ppn_nominal,
    p.total_setelah_pajak,
    p.biaya_pengiriman,
    p.total_akhir,
    p.tanggal_jatuh_tempo,
    p.syarat_pembayaran,
    p.metode_pembayaran,
    p.nomor_po_supplier,
    p.alamat_pengiriman,
    p.keterangan,
    p.created_at,
    p.updated_at,
    p.created_by,
    p.updated_by,
    p.approved_by,
    p.approved_at,
    -- Hitung total yang sudah dibayar
    COALESCE(SUM(pp.jumlah_bayar), 0) as total_dibayar,
    -- Hitung sisa yang belum dibayar
    (p.total_akhir - COALESCE(SUM(pp.jumlah_bayar), 0)) as sisa_pembayaran,
    -- Status pembayaran
    CASE 
        WHEN COALESCE(SUM(pp.jumlah_bayar), 0) = 0 THEN 'belum_bayar'
        WHEN COALESCE(SUM(pp.jumlah_bayar), 0) < p.total_akhir THEN 'sebagian'
        WHEN COALESCE(SUM(pp.jumlah_bayar), 0) >= p.total_akhir THEN 'lunas'
        ELSE 'belum_bayar'
    END as status_pembayaran
FROM pembelian p
LEFT JOIN supplier s ON p.id_supplier = s.id
LEFT JOIN pembelian_pembayaran pp ON p.id = pp.id_pembelian AND pp.status = 'verified'
GROUP BY p.id
ORDER BY p.tanggal_pembelian DESC, p.id DESC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_pengiriman_detail`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_pengiriman_detail` AS SELECT 
    pd.id,
    pd.id_pengiriman,
    p.nomor_pengiriman,
    p.tanggal_pengiriman,
    p.status as status_pengiriman,
    pd.id_pesanan_detail,
    pd.id_barang,
    b.kode_barang,
    b.nama_barang,
    b.merk,
    b.tipe,
    s.kode_satuan,
    s.nama_satuan,
    pd.qty_dipesan,
    pd.qty_dikirim,
    pd.qty_sisa,
    pd.harga_satuan,
    pd.subtotal,
    pd.berat_satuan,
    pd.total_berat,
    pd.keterangan
FROM pengiriman_detail pd
LEFT JOIN pengiriman p ON pd.id_pengiriman = p.id
LEFT JOIN barang b ON pd.id_barang = b.id
LEFT JOIN satuan s ON b.satuan_id = s.id
ORDER BY p.tanggal_pengiriman DESC, b.nama_barang ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_pengiriman_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_pengiriman_summary` AS SELECT 
    p.id,
    p.nomor_pengiriman,
    p.tanggal_pengiriman,
    p.id_pesanan,
    ps.nomor_pesanan,
    p.id_pelanggan,
    pel.kode as kode_pelanggan,
    pel.nama as nama_pelanggan,
    pel.alamat as alamat_pelanggan,
    pel.no_telepon,
    p.id_gudang,
    g.kode_gudang,
    g.nama_gudang,
    p.jenis_pengiriman,
    p.status,
    p.ekspedisi,
    p.nomor_resi,
    p.biaya_pengiriman,
    p.alamat_pengiriman,
    p.nama_penerima,
    p.telepon_penerima,
    p.catatan_pengiriman,
    p.tanggal_estimasi,
    p.tanggal_terkirim,
    p.total_item,
    p.total_qty,
    p.total_berat,
    p.created_at,
    p.updated_at,
    p.created_by,
    p.updated_by
FROM pengiriman p
LEFT JOIN pesanan ps ON p.id_pesanan = ps.id
LEFT JOIN pelanggan pel ON p.id_pelanggan = pel.id
LEFT JOIN gudang g ON p.id_gudang = g.id
ORDER BY p.tanggal_pengiriman DESC, p.id DESC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_stok_movement_detail`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_stok_movement_detail` AS SELECT `sm`.`id` AS `id`, `sm`.`tanggal` AS `tanggal`, `b`.`kode_barang` AS `kode_barang`, `b`.`nama_barang` AS `nama_barang`, `g`.`kode_gudang` AS `kode_gudang`, `g`.`nama_gudang` AS `nama_gudang`, `sm`.`tipe_transaksi` AS `tipe_transaksi`, `sm`.`qty_in` AS `qty_in`, `sm`.`qty_out` AS `qty_out`, `sm`.`keterangan` AS `keterangan`, `sm`.`ref_transaksi` AS `ref_transaksi`, `sm`.`user_input` AS `user_input`, `sm`.`created_at` AS `created_at` FROM ((`stok_movement` `sm` join `barang` `b` on(`sm`.`id_barang` = `b`.`id`)) join `gudang` `g` on(`sm`.`id_gudang` = `g`.`id`)) ORDER BY `sm`.`tanggal` DESC, `sm`.`id` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_stok_opname_detail`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_stok_opname_detail` AS SELECT `sod`.`id` AS `id`, `sod`.`id_opname` AS `id_opname`, `so`.`nomor_opname` AS `nomor_opname`, `so`.`tanggal_opname` AS `tanggal_opname`, `so`.`status` AS `status`, `g`.`kode_gudang` AS `kode_gudang`, `g`.`nama_gudang` AS `nama_gudang`, `b`.`kode_barang` AS `kode_barang`, `b`.`nama_barang` AS `nama_barang`, `sod`.`qty_sistem` AS `qty_sistem`, `sod`.`qty_fisik` AS `qty_fisik`, `sod`.`selisih` AS `selisih`, `sod`.`keterangan` AS `keterangan`, `sod`.`created_at` AS `created_at` FROM (((`stok_opname_detail` `sod` join `stok_opname` `so` on(`sod`.`id_opname` = `so`.`id`)) join `barang` `b` on(`sod`.`id_barang` = `b`.`id`)) join `gudang` `g` on(`so`.`id_gudang` = `g`.`id`)) ORDER BY `so`.`tanggal_opname` DESC, `b`.`nama_barang` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_stok_opname_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_stok_opname_summary` AS SELECT `so`.`id` AS `id`, `so`.`nomor_opname` AS `nomor_opname`, `so`.`tanggal_opname` AS `tanggal_opname`, `g`.`kode_gudang` AS `kode_gudang`, `g`.`nama_gudang` AS `nama_gudang`, `so`.`status` AS `status`, `so`.`total_item` AS `total_item`, `so`.`total_selisih_positif` AS `total_selisih_positif`, `so`.`total_selisih_negatif` AS `total_selisih_negatif`, `so`.`keterangan` AS `keterangan`, `so`.`user_input` AS `user_input`, `so`.`user_final` AS `user_final`, `so`.`tanggal_final` AS `tanggal_final`, `so`.`created_at` AS `created_at` FROM (`stok_opname` `so` join `gudang` `g` on(`so`.`id_gudang` = `g`.`id`)) ORDER BY `so`.`tanggal_opname` DESC, `so`.`id` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_stok_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_stok_summary` AS SELECT `sb`.`id_barang` AS `id_barang`, `b`.`kode_barang` AS `kode_barang`, `b`.`nama_barang` AS `nama_barang`, `sb`.`id_gudang` AS `id_gudang`, `g`.`kode_gudang` AS `kode_gudang`, `g`.`nama_gudang` AS `nama_gudang`, `sb`.`qty_terakhir` AS `qty_terakhir`, `sb`.`updated_at` AS `updated_at` FROM ((`stok_barang` `sb` join `barang` `b` on(`sb`.`id_barang` = `b`.`id`)) join `gudang` `g` on(`sb`.`id_gudang` = `g`.`id`)) ORDER BY `b`.`nama_barang` ASC, `g`.`nama_gudang` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_transfer_stok_detail`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_transfer_stok_detail` AS SELECT `tsd`.`id` AS `id`, `tsd`.`transfer_stok_id` AS `transfer_stok_id`, `ts`.`nomor_transfer` AS `nomor_transfer`, `ts`.`tanggal_transfer` AS `tanggal_transfer`, `ts`.`status` AS `status`, `tsd`.`barang_id` AS `barang_id`, `b`.`kode_barang` AS `kode_barang`, `b`.`nama_barang` AS `nama_barang`, `tsd`.`qty` AS `qty`, `s`.`kode_satuan` AS `kode_satuan`, `s`.`nama_satuan` AS `nama_satuan`, `ga`.`nama_gudang` AS `nama_gudang_asal`, `gt`.`nama_gudang` AS `nama_gudang_tujuan`, `tsd`.`keterangan` AS `keterangan` FROM (((((`transfer_stok_detail` `tsd` left join `transfer_stok` `ts` on(`tsd`.`transfer_stok_id` = `ts`.`id`)) left join `barang` `b` on(`tsd`.`barang_id` = `b`.`id`)) left join `satuan` `s` on(`tsd`.`satuan_id` = `s`.`id`)) left join `gudang` `ga` on(`ts`.`gudang_asal_id` = `ga`.`id`)) left join `gudang` `gt` on(`ts`.`gudang_tujuan_id` = `gt`.`id`)) ORDER BY `ts`.`tanggal_transfer` DESC, `b`.`nama_barang` ASC 
;

-- Removing temporary table and create final VIEW structure
DROP TABLE IF EXISTS `v_transfer_stok_summary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_transfer_stok_summary` AS SELECT `ts`.`id` AS `id`, `ts`.`nomor_transfer` AS `nomor_transfer`, `ts`.`tanggal_transfer` AS `tanggal_transfer`, `ts`.`status` AS `status`, `ts`.`total_item` AS `total_item`, `ts`.`total_qty` AS `total_qty`, `ga`.`kode_gudang` AS `kode_gudang_asal`, `ga`.`nama_gudang` AS `nama_gudang_asal`, `gt`.`kode_gudang` AS `kode_gudang_tujuan`, `gt`.`nama_gudang` AS `nama_gudang_tujuan`, `ts`.`keterangan` AS `keterangan`, `ts`.`tanggal_kirim` AS `tanggal_kirim`, `ts`.`tanggal_terima` AS `tanggal_terima`, `ts`.`dibuat_oleh` AS `dibuat_oleh`, `ts`.`dikirim_oleh` AS `dikirim_oleh`, `ts`.`diterima_oleh` AS `diterima_oleh`, `ts`.`dibuat_pada` AS `dibuat_pada` FROM ((`transfer_stok` `ts` left join `gudang` `ga` on(`ts`.`gudang_asal_id` = `ga`.`id`)) left join `gudang` `gt` on(`ts`.`gudang_tujuan_id` = `gt`.`id`)) ORDER BY `ts`.`id` ASC 
;

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
