/**
 * Status Badges CSS - Standardized Status Styling
 * To<PERSON> Elektronik - Consistent Status Display
 */

/* =====================================================
   BASE BADGE STYLES
   ===================================================== */

.badge {
    font-size: 0.75em;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.badge i {
    font-size: 0.875em;
}

/* =====================================================
   DRAFT STATUS - Warning Yellow
   ===================================================== */

.badge-draft {
    background-color: #ffc107 !important;
    color: #212529 !important;
    border: 1px solid #ffca2c;
}

.badge-draft:hover {
    background-color: #ffcd39 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

/* =====================================================
   CONFIRMED/APPROVED STATUS - Info Blue
   ===================================================== */

.badge-confirmed,
.badge-approved {
    background-color: #17a2b8 !important;
    color: white !important;
    border: 1px solid #1fc7d4;
}

.badge-confirmed:hover,
.badge-approved:hover {
    background-color: #1fc7d4 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

/* =====================================================
   SHIPPED/ORDERED STATUS - Primary Purple
   ===================================================== */

.badge-shipped,
.badge-ordered {
    background-color: #6f42c1 !important;
    color: white !important;
    border: 1px solid #7952cc;
}

.badge-shipped:hover,
.badge-ordered:hover {
    background-color: #7952cc !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(111, 66, 193, 0.3);
}

/* =====================================================
   COMPLETED/FINAL STATUS - Success Green
   ===================================================== */

.badge-completed,
.badge-final {
    background-color: #28a745 !important;
    color: white !important;
    border: 1px solid #34ce57;
}

.badge-completed:hover,
.badge-final:hover {
    background-color: #34ce57 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

/* =====================================================
   CANCELLED STATUS - Danger Red
   ===================================================== */

.badge-cancelled {
    background-color: #dc3545 !important;
    color: white !important;
    border: 1px solid #e4606d;
}

.badge-cancelled:hover {
    background-color: #e4606d !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* =====================================================
   OVERDUE STATUS - Warning Orange
   ===================================================== */

.badge-overdue {
    background-color: #fd7e14 !important;
    color: white !important;
    border: 1px solid #fd8c3a;
}

.badge-overdue:hover {
    background-color: #fd8c3a !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(253, 126, 20, 0.3);
}

/* =====================================================
   PENDING STATUS - Secondary Gray
   ===================================================== */

.badge-pending {
    background-color: #6c757d !important;
    color: white !important;
    border: 1px solid #7a8288;
}

.badge-pending:hover {
    background-color: #7a8288 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

/* =====================================================
   SHIPPING SPECIFIC STATUSES
   ===================================================== */

.badge-prepared {
    background-color: #ffc107 !important;
    color: #212529 !important;
    border: 1px solid #ffca2c;
}

.badge-in_transit {
    background-color: #17a2b8 !important;
    color: white !important;
    border: 1px solid #1fc7d4;
}

.badge-delivered {
    background-color: #28a745 !important;
    color: white !important;
    border: 1px solid #34ce57;
}

.badge-returned {
    background-color: #dc3545 !important;
    color: white !important;
    border: 1px solid #e4606d;
}

/* =====================================================
   FINANCIAL SPECIFIC STATUSES
   ===================================================== */

.badge-issued {
    background-color: #17a2b8 !important;
    color: white !important;
    border: 1px solid #1fc7d4;
}

.badge-paid {
    background-color: #28a745 !important;
    color: white !important;
    border: 1px solid #34ce57;
}

.badge-verified {
    background-color: #28a745 !important;
    color: white !important;
    border: 1px solid #34ce57;
}

.badge-rejected {
    background-color: #dc3545 !important;
    color: white !important;
    border: 1px solid #e4606d;
}

/* =====================================================
   PURCHASE SPECIFIC STATUSES
   ===================================================== */

.badge-received {
    background-color: #6c757d !important;
    color: white !important;
    border: 1px solid #7a8288;
}

/* =====================================================
   SIZE VARIATIONS
   ===================================================== */

.badge-sm {
    font-size: 0.65em;
    padding: 0.25rem 0.5rem;
}

.badge-lg {
    font-size: 0.875em;
    padding: 0.5rem 1rem;
}

/* =====================================================
   ANIMATION EFFECTS
   ===================================================== */

.badge {
    transition: all 0.2s ease-in-out;
}

.badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Pulse animation for active statuses */
.badge-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

/* =====================================================
   STATUS ICONS
   ===================================================== */

.badge-draft::before {
    content: "\f15c";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 0.25rem;
}

.badge-confirmed::before,
.badge-approved::before {
    content: "\f00c";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 0.25rem;
}

.badge-shipped::before {
    content: "\f48b";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 0.25rem;
}

.badge-completed::before,
.badge-final::before {
    content: "\f560";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 0.25rem;
}

.badge-cancelled::before {
    content: "\f00d";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 0.25rem;
}

/* =====================================================
   RESPONSIVE DESIGN
   ===================================================== */

@media (max-width: 768px) {
    .badge {
        font-size: 0.7em;
        padding: 0.25rem 0.5rem;
    }
    
    .badge::before {
        display: none;
    }
}

/* =====================================================
   DARK MODE SUPPORT
   ===================================================== */

@media (prefers-color-scheme: dark) {
    .badge-draft {
        background-color: #e0a800 !important;
        color: #000 !important;
    }
    
    .badge-confirmed,
    .badge-approved,
    .badge-issued {
        background-color: #138496 !important;
    }
    
    .badge-shipped,
    .badge-ordered {
        background-color: #5a32a3 !important;
    }
    
    .badge-completed,
    .badge-final,
    .badge-paid,
    .badge-verified {
        background-color: #1e7e34 !important;
    }
    
    .badge-cancelled,
    .badge-rejected,
    .badge-returned {
        background-color: #bd2130 !important;
    }
    
    .badge-overdue {
        background-color: #dc6545 !important;
    }
    
    .badge-pending,
    .badge-received {
        background-color: #545b62 !important;
    }
}

/* =====================================================
   PRINT STYLES
   ===================================================== */

@media print {
    .badge {
        background-color: transparent !important;
        color: #000 !important;
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .badge::before {
        display: none;
    }
}
