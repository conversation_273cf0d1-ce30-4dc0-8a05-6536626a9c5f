<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Controller Pengiriman
 * Mengatur pengiriman barang berdasarkan pesanan
 * Terintegrasi dengan stok gudang dan validasi
 */
class Pengiriman extends MY_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model(array('Mod_pengiriman', 'Mod_dashboard'));
        $this->load->library('form_validation');
    }

    public function index()
    {
        $link = 'pengiriman';
        $level = $this->session->userdata('id_level');

        // Simplified access control - default to allow access for testing
        $akses = "Y";

        try {
            $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
            if ($jml > 0) {
                $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
                $akses = $data['akses_menu']->view;
            } else {
                $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
                $akses = $data['akses_menu']->view;
            }
        } catch (Exception $e) {
            // If access control fails, default to allow access
            log_message('error', 'Access control error in Pengiriman: ' . $e->getMessage());
            $akses = "Y";
        }

        $data['level'] = $this->session->userdata('level');
        $data['akses'] = $akses;
        $data['link'] = $link;

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'pengiriman/pengiriman', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list()
    {
        ini_set('memory_limit', '512M');
        set_time_limit(3600);
        $list = $this->Mod_pengiriman->get_datatables();
        $data = array();
        $no = $_POST['start'];
        foreach ($list as $pengiriman) {
            $no++;
            $row = array();
            $row[] = $pengiriman->nomor_pengiriman;
            $row[] = date('d/m/Y', strtotime($pengiriman->tanggal_pengiriman));
            $row[] = $pengiriman->nama_pelanggan ? 
                     $pengiriman->nama_pelanggan . '<br><small class="text-muted">' . $pengiriman->kode_pelanggan . '</small>' : 
                     '<span class="text-muted">-</span>';
            $row[] = $pengiriman->nomor_pesanan ? 
                     '<a href="javascript:void(0)" onclick="detail_pesanan(' . $pengiriman->id_pesanan . ')" class="text-primary">' . $pengiriman->nomor_pesanan . '</a>' : 
                     '<span class="text-muted">-</span>';
            $row[] = $pengiriman->ekspedisi ?: '<span class="text-muted">-</span>';
            $row[] = $pengiriman->nomor_resi ?: '<span class="text-muted">-</span>';
            
            // Status badge
            $status_class = $this->get_status_class($pengiriman->status);
            $row[] = '<span class="badge badge-' . $status_class . '">' . ucfirst($pengiriman->status) . '</span>';
            
            $row[] = number_format($pengiriman->total_item, 0);
            $row[] = number_format($pengiriman->total_qty, 2);
            
            // Action buttons
            $row[] = '
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-info" onclick="detail(' . $pengiriman->id . ')" title="Detail"><i class="fa fa-eye"></i></button>
                    <button type="button" class="btn btn-sm btn-warning" onclick="edit(' . $pengiriman->id . ')" title="Edit"><i class="fa fa-edit"></i></button>
                    <button type="button" class="btn btn-sm btn-danger" onclick="hapus(' . $pengiriman->id . ')" title="Hapus"><i class="fa fa-trash"></i></button>
                </div>';

            $data[] = $row;
        }

        $output = array(
            "draw" => $_POST['draw'],
            "recordsTotal" => $this->Mod_pengiriman->count_all(),
            "recordsFiltered" => $this->Mod_pengiriman->count_filtered(),
            "data" => $data,
        );
        echo json_encode($output);
    }

    private function get_status_class($status)
    {
        switch ($status) {
            case 'draft':
                return 'draft';
            case 'prepared':
                return 'prepared';
            case 'shipped':
                return 'shipped';
            case 'in_transit':
                return 'in_transit';
            case 'delivered':
                return 'delivered';
            case 'returned':
                return 'returned';
            case 'cancelled':
                return 'cancelled';
            default:
                return 'secondary';
        }
    }

    // Method untuk form input modal - Simplified without access control
    public function form_input()
    {
        // Set content type
        header('Content-Type: text/html; charset=utf-8');

        try {
            // Initialize data array with defaults
            $data = array(
                'pesanan_list' => array(),
                'pelanggan_list' => array(),
                'nomor_pengiriman' => 'PGR-' . date('Ymd') . '-0001'
            );

            // Try to load data from model with individual error handling
            try {
                $pesanan_data = $this->Mod_pengiriman->get_pesanan_for_shipping();
                if (is_array($pesanan_data)) {
                    $data['pesanan_list'] = $pesanan_data;
                }
            } catch (Exception $e) {
                log_message('error', 'Error loading pesanan in form_input: ' . $e->getMessage());
                // Keep empty array as default
            }

            try {
                $pelanggan_data = $this->Mod_pengiriman->get_pelanggan_dropdown();
                if (is_array($pelanggan_data)) {
                    $data['pelanggan_list'] = $pelanggan_data;
                }
            } catch (Exception $e) {
                log_message('error', 'Error loading pelanggan in form_input: ' . $e->getMessage());
                // Keep empty array as default
            }

            try {
                $nomor = $this->Mod_pengiriman->generate_nomor_pengiriman();
                if (!empty($nomor)) {
                    $data['nomor_pengiriman'] = $nomor;
                }
            } catch (Exception $e) {
                log_message('error', 'Error generating nomor in form_input: ' . $e->getMessage());
                // Keep default nomor
            }

            // Load the view
            $this->load->view('pengiriman/form_input', $data);

        } catch (Exception $e) {
            // Log the error
            log_message('error', 'Critical error in Pengiriman form_input: ' . $e->getMessage());

            // Show user-friendly error message
            echo '<div class="alert alert-danger">
                    <h5><i class="fa fa-exclamation-triangle"></i> Terjadi kesalahan saat memuat form</h5>
                    <p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>
                    <hr>
                    <p><strong>Informasi Debug:</strong></p>
                    <ul>
                        <li>Controller: Pengiriman</li>
                        <li>Method: form_input</li>
                        <li>Time: ' . date('Y-m-d H:i:s') . '</li>
                        <li>File: ' . __FILE__ . '</li>
                        <li>Line: ' . __LINE__ . '</li>
                    </ul>
                    <p><strong>Kemungkinan penyebab:</strong></p>
                    <ul>
                        <li>Database connection error</li>
                        <li>Missing tables (pesanan, pelanggan, pengiriman)</li>
                        <li>Model file not found or corrupted</li>
                        <li>View file not found</li>
                    </ul>
                    <p><strong>Solusi:</strong></p>
                    <ul>
                        <li>Jalankan: <code>mysql -u root -p toko_elektronik < DB/pengiriman_setup.sql</code></li>
                        <li>Periksa file: application/models/Mod_pengiriman.php</li>
                        <li>Periksa file: application/views/pengiriman/form_input.php</li>
                        <li>Test dengan: <a href="' . site_url('pengiriman_simple/form_input') . '">Controller sederhana</a></li>
                    </ul>
                  </div>';
        }
    }

    public function generate_nomor()
    {
        $nomor = $this->Mod_pengiriman->generate_nomor_pengiriman();
        echo json_encode(array('nomor' => $nomor));
    }

    public function get_pesanan_detail()
    {
        $id_pesanan = $this->input->post('id_pesanan');
        if ($id_pesanan) {
            $detail = $this->Mod_pengiriman->get_pesanan_detail($id_pesanan);
            echo json_encode($detail);
        } else {
            echo json_encode(array());
        }
    }

    public function get_gudang_with_stock()
    {
        $id_barang = $this->input->post('id_barang');
        if ($id_barang) {
            $gudang = $this->Mod_pengiriman->get_gudang_with_stock($id_barang);
            echo json_encode($gudang);
        } else {
            echo json_encode(array());
        }
    }

    public function insert()
    {
        $this->_validate();

        // Generate nomor otomatis jika tidak diisi
        $nomor = $this->input->post('nomor_pengiriman');
        if (empty($nomor)) {
            $nomor = $this->Mod_pengiriman->generate_nomor_pengiriman();
        }

        $save = array(
            'nomor_pengiriman' => $nomor,
            'tanggal_pengiriman' => $this->input->post('tanggal_pengiriman'),
            'id_pesanan' => $this->input->post('id_pesanan'),
            'id_pelanggan' => $this->input->post('id_pelanggan'),
            'alamat_pengiriman' => $this->input->post('alamat_pengiriman'),
            'ekspedisi' => $this->input->post('ekspedisi'),
            'nomor_resi' => $this->input->post('nomor_resi'),
            'biaya_pengiriman' => $this->input->post('biaya_pengiriman') ?: 0,
            'status' => $this->input->post('status') ?: 'draft',
            'estimasi_tiba' => $this->input->post('estimasi_tiba'),
            'keterangan' => $this->input->post('keterangan'),
            'created_by' => $this->session->userdata('nama_user'),
        );
        
        $id_pengiriman = $this->Mod_pengiriman->insert($save);
        
        echo json_encode(array(
            "status" => TRUE,
            "id_pengiriman" => $id_pengiriman
        ));
    }

    public function update()
    {
        $this->_validate();

        $id = $this->input->post('id');

        $save = array(
            'tanggal_pengiriman' => $this->input->post('tanggal_pengiriman'),
            'id_pesanan' => $this->input->post('id_pesanan'),
            'id_pelanggan' => $this->input->post('id_pelanggan'),
            'alamat_pengiriman' => $this->input->post('alamat_pengiriman'),
            'ekspedisi' => $this->input->post('ekspedisi'),
            'nomor_resi' => $this->input->post('nomor_resi'),
            'biaya_pengiriman' => $this->input->post('biaya_pengiriman') ?: 0,
            'status' => $this->input->post('status'),
            'estimasi_tiba' => $this->input->post('estimasi_tiba'),
            'keterangan' => $this->input->post('keterangan'),
            'updated_by' => $this->session->userdata('nama_user'),
        );

        $this->Mod_pengiriman->update($id, $save);
        echo json_encode(array("status" => TRUE));
    }

    public function edit($id)
    {
        $data = $this->Mod_pengiriman->get($id);
        echo json_encode($data);
    }

    public function hapus($id)
    {
        $this->Mod_pengiriman->delete($id);
        echo json_encode(array("status" => TRUE));
    }

    private function _validate()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        $tanggal_pengiriman = $this->input->post('tanggal_pengiriman');
        if (empty($tanggal_pengiriman)) {
            $data['inputerror'][] = 'tanggal_pengiriman';
            $data['error_string'][] = 'Tanggal pengiriman harus diisi';
            $data['status'] = FALSE;
        }

        if ($this->input->post('id_pesanan') == '') {
            $data['inputerror'][] = 'id_pesanan';
            $data['error_string'][] = 'Pesanan harus dipilih';
            $data['status'] = FALSE;
        }

        if ($this->input->post('id_pelanggan') == '') {
            $data['inputerror'][] = 'id_pelanggan';
            $data['error_string'][] = 'Pelanggan harus dipilih';
            $data['status'] = FALSE;
        }

        if ($this->input->post('alamat_pengiriman') == '') {
            $data['inputerror'][] = 'alamat_pengiriman';
            $data['error_string'][] = 'Alamat pengiriman harus diisi';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    // ========== DETAIL METHODS ==========

    public function detail($id)
    {
        $link = 'pengiriman';
        $level = $this->session->userdata('id_level');

        $jml = $this->Mod_dashboard->get_akses_menu($link, $level)->num_rows();
        if ($jml > 0) {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_menu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        } else {
            $data['akses_menu'] = $this->Mod_dashboard->get_akses_submenu($link, $level)->row();
            $akses = $data['akses_menu']->view;
        }

        $data = array(
            'level' => $this->session->userdata('level'),
            'akses' => $akses,
            'link' => $link,
            'id_pengiriman' => $id,
            'pengiriman_data' => $this->Mod_pengiriman->get($id),
            'barang_list' => $this->Mod_pengiriman->get_barang_dropdown(),
            'gudang_list' => $this->Mod_pengiriman->get_gudang_dropdown()
        );

        if ($akses == "Y") {
            $this->template->load('layoutbackend', 'pengiriman/detail_pengiriman', $data);
        } else {
            $data['page'] = $link;
            $this->template->load('layoutbackend', 'admin/akses_ditolak', $data);
        }
    }

    public function ajax_list_detail($id_pengiriman)
    {
        $list = $this->Mod_pengiriman->get_detail($id_pengiriman);
        $data = array();
        $no = 0;
        foreach ($list as $detail) {
            $no++;
            $row = array();
            $row[] = $no;
            $row[] = $detail->kode_barang . '<br><small class="text-muted">' . $detail->nama_barang . '</small>';
            $row[] = $detail->nama_gudang . '<br><small class="text-muted">' . $detail->kode_gudang . '</small>';
            $row[] = number_format($detail->qty_dikirim, 2) . ' ' . $detail->nama_satuan;
            $row[] = number_format($detail->berat_satuan, 2) . ' kg';
            $row[] = number_format($detail->total_berat, 2) . ' kg';
            $row[] = $detail->keterangan ?: '<span class="text-muted">-</span>';

            // Action buttons
            $row[] = '
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-warning" onclick="edit_detail(' . $detail->id . ')" title="Edit"><i class="fa fa-edit"></i></button>
                    <button type="button" class="btn btn-sm btn-danger" onclick="hapus_detail(' . $detail->id . ')" title="Hapus"><i class="fa fa-trash"></i></button>
                </div>';

            $data[] = $row;
        }

        $output = array(
            "data" => $data,
        );
        echo json_encode($output);
    }

    public function insert_detail()
    {
        $this->_validate_detail();

        $id_pengiriman = $this->input->post('id_pengiriman');
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');
        $qty_dikirim = $this->input->post('qty_dikirim');

        // Validasi stok
        $stock_validation = $this->Mod_pengiriman->validate_stock($id_barang, $id_gudang, $qty_dikirim);

        if (!$stock_validation['is_sufficient']) {
            echo json_encode(array(
                "status" => FALSE,
                "message" => "Stok tidak mencukupi. Tersedia: " . $stock_validation['available_stock']
            ));
            return;
        }

        $save = array(
            'id_pengiriman' => $id_pengiriman,
            'id_barang' => $id_barang,
            'id_gudang' => $id_gudang,
            'qty_dikirim' => $qty_dikirim,
            'berat_satuan' => $this->input->post('berat_satuan') ?: 0,
            'keterangan' => $this->input->post('keterangan_detail')
        );

        $this->Mod_pengiriman->insert_detail($save);

        // Update total pengiriman
        $this->Mod_pengiriman->update_total_pengiriman($id_pengiriman);

        echo json_encode(array("status" => TRUE));
    }

    public function update_detail()
    {
        $this->_validate_detail();

        $id = $this->input->post('detail_id');
        $id_pengiriman = $this->input->post('id_pengiriman');
        $id_barang = $this->input->post('id_barang');
        $id_gudang = $this->input->post('id_gudang');
        $qty_dikirim = $this->input->post('qty_dikirim');

        // Validasi stok (exclude current detail)
        $stock_validation = $this->Mod_pengiriman->validate_stock($id_barang, $id_gudang, $qty_dikirim, $id);

        if (!$stock_validation['is_sufficient']) {
            echo json_encode(array(
                "status" => FALSE,
                "message" => "Stok tidak mencukupi. Tersedia: " . $stock_validation['available_stock']
            ));
            return;
        }

        $save = array(
            'id_barang' => $id_barang,
            'id_gudang' => $id_gudang,
            'qty_dikirim' => $qty_dikirim,
            'berat_satuan' => $this->input->post('berat_satuan') ?: 0,
            'keterangan' => $this->input->post('keterangan_detail')
        );

        $this->Mod_pengiriman->update_detail($id, $save);

        // Update total pengiriman
        $this->Mod_pengiriman->update_total_pengiriman($id_pengiriman);

        echo json_encode(array("status" => TRUE));
    }

    public function edit_detail($id)
    {
        $data = $this->Mod_pengiriman->get_detail_by_id($id);
        echo json_encode($data);
    }

    public function hapus_detail($id)
    {
        // Get detail info before delete
        $detail = $this->Mod_pengiriman->get_detail_by_id($id);
        $id_pengiriman = $detail->id_pengiriman;

        $this->Mod_pengiriman->delete_detail($id);

        // Update total pengiriman
        $this->Mod_pengiriman->update_total_pengiriman($id_pengiriman);

        echo json_encode(array("status" => TRUE));
    }

    private function _validate_detail()
    {
        $data = array();
        $data['error_string'] = array();
        $data['inputerror'] = array();
        $data['status'] = TRUE;

        if ($this->input->post('id_barang') == '') {
            $data['inputerror'][] = 'id_barang';
            $data['error_string'][] = 'Barang harus dipilih';
            $data['status'] = FALSE;
        }

        if ($this->input->post('id_gudang') == '') {
            $data['inputerror'][] = 'id_gudang';
            $data['error_string'][] = 'Gudang harus dipilih';
            $data['status'] = FALSE;
        }

        if ($this->input->post('qty_dikirim') == '' || $this->input->post('qty_dikirim') <= 0) {
            $data['inputerror'][] = 'qty_dikirim';
            $data['error_string'][] = 'Qty dikirim harus diisi dan lebih dari 0';
            $data['status'] = FALSE;
        }

        if ($data['status'] === FALSE) {
            echo json_encode($data);
            exit();
        }
    }

    // Finalisasi pengiriman
    public function finalize($id)
    {
        $result = $this->Mod_pengiriman->finalize_pengiriman($id, $this->session->userdata('nama_user'));

        if ($result) {
            echo json_encode(array(
                "status" => TRUE,
                "message" => "Pengiriman berhasil difinalisasi dan stok telah diupdate"
            ));
        } else {
            echo json_encode(array(
                "status" => FALSE,
                "message" => "Gagal finalisasi pengiriman. Pastikan status masih 'prepared'"
            ));
        }
    }
}
